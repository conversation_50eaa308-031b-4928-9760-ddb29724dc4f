package com.baidu.keyue.deepsight.schedule;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.List;

@ExtendWith(MockitoExtension.class)
public class MetricAggSchedulerTest {

    @Mock
    private RedissonClient redisson;

    @Mock
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @Mock
    private RLock lock;

    private MetricAggScheduler metricAggScheduler;

    @BeforeEach
    void setUp() {
        metricAggScheduler = new MetricAggScheduler();
    }

    @Test
    void aiobSessionMetricAggWhenGetTablesThrowsExceptionShouldUnlock() {
        when(redisson.getLock("aiobSessionMetricAgg-Lock")).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(aiobSessionMetricAggService.getSessionAggTables()).thenThrow(new RuntimeException("Test exception"));

        assertDoesNotThrow(() -> metricAggScheduler.aiobSessionMetricAgg());

        verify(aiobSessionMetricAggService, never()).aiobSessionMetricAggExec(any());
        verify(lock, times(1)).unlock();
    }

    @Test
    void generateSchedulerLockKeyShouldReturnJoinedStringWithHyphen() {
        String result = metricAggScheduler.generateSchedulerLockKey("key1", "key2", "key3");
        assertEquals("key1-key2-key3", result);
    }

    @Test
    void generateSchedulerLockKeyWithSingleKeyShouldReturnSameKey() {
        String result = metricAggScheduler.generateSchedulerLockKey("singleKey");
        assertEquals("singleKey", result);
    }

    @Test
    void generateSchedulerLockKeyWithEmptyKeysShouldReturnEmptyString() {
        String result = metricAggScheduler.generateSchedulerLockKey();
        assertEquals("", result);
    }

    @Test
    void aiobSessionMetricAggWhenAggregationFailsShouldContinueWithNextTable() {
        when(redisson.getLock("aiobSessionMetricAgg-Lock")).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(aiobSessionMetricAggService.getSessionAggTables()).thenReturn(List.of("table1", "table2"));
        doThrow(new RuntimeException("Test exception")).when(aiobSessionMetricAggService).aiobSessionMetricAggExec("table1");

        assertDoesNotThrow(() -> metricAggScheduler.aiobSessionMetricAgg());

        verify(aiobSessionMetricAggService, times(1)).aiobSessionMetricAggExec("table1");
        verify(aiobSessionMetricAggService, times(1)).aiobSessionMetricAggExec("table2");
        verify(lock, times(1)).unlock();
    }

    @Test
    void aiobSessionMetricAggWhenLockAcquiredShouldExecuteAggregation() {
        when(redisson.getLock("aiobSessionMetricAgg-Lock")).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(aiobSessionMetricAggService.getSessionAggTables()).thenReturn(List.of("table1", "table2"));

        assertDoesNotThrow(() -> metricAggScheduler.aiobSessionMetricAgg());

        verify(aiobSessionMetricAggService, times(1)).getSessionAggTables();
        verify(aiobSessionMetricAggService, times(1)).aiobSessionMetricAggExec("table1");
        verify(aiobSessionMetricAggService, times(1)).aiobSessionMetricAggExec("table2");
        verify(lock, times(1)).unlock();
    }

    @Test
    void aiobSessionMetricAggWhenLockNotAcquiredShouldThrowBusyRequestException() {
        when(redisson.getLock("aiobSessionMetricAgg-Lock")).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);

        DeepSightException.BusyRequestException exception = assertThrows(
                DeepSightException.BusyRequestException.class,
                () -> metricAggScheduler.aiobSessionMetricAgg()
        );

        assertEquals(ErrorCode.BUSY_REQUEST, exception.getErrorCode());
        verify(aiobSessionMetricAggService, never()).getSessionAggTables();
        verify(lock, never()).unlock();
    }

}