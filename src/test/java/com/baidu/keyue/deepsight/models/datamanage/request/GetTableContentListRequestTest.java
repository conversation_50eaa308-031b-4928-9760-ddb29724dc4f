package com.baidu.keyue.deepsight.models.datamanage.request;

import static org.junit.jupiter.api.Assertions.*;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;

import org.junit.jupiter.api.Test;

import java.util.Collections;

public class GetTableContentListRequestTest{

    // testBuildExternalRequest 用于测试 buildExternalRequest
    // generated by Comate
    @Test
    void testBuildExternalRequest() {
        // Arrange
        Long fileId = 123L;
        String praValue = "testValue";
        Long dataTableId = 456L;
    
        // Act
        GetTableContentListRequest result = GetTableContentListRequest.buildExternalRequest(fileId, praValue, dataTableId);
    
        // Assert
        assertNotNull(result);
        assertEquals(dataTableId, result.getDataTableId());
        assertEquals(1, result.getPageNo());
        assertEquals(1, result.getPageSize());
    
        assertNotNull(result.getFilters());
        assertEquals(1, result.getFilters().size());
    
        RuleFilter filter = result.getFilters().get(0);
        assertEquals(fileId, filter.getFieldId());
        assertEquals(FilterTypeEnum.STRING, filter.getType());
        assertEquals(FuncEnum.CONTAIN, filter.getFunction());
        assertEquals(Collections.singletonList(praValue), filter.getParams());
    
        assertNotNull(result.getDecryptedOutputField());
        assertEquals(1, result.getDecryptedOutputField().size());
        assertTrue(result.getDecryptedOutputField().contains(Constants.TABLE_MOBILE_FIELD));
    }

}