package com.baidu.keyue.deepsight.service.datamanage.impl;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.DeleteStatusEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldEnumMappingDTO;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.DeleteTableRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenFieldEnumRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenTableInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableUserInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldEnumResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.exception.ServiceException;
import com.baidu.keyue.deepsight.models.predict.PredictDataSet;
import com.baidu.keyue.deepsight.models.predict.PredictDataSource;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldEncryConfigMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.ai.AiBaseService;
import com.baidu.keyue.deepsight.service.datamanage.DbConfService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;
import com.baidu.keyue.deepsight.utils.XID;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@TestPropertySource(properties = {
    "data-table-manager.default-show-fields-size=10",
    "ai.qianfan.authorization=testToken",
    "ai.template.genField=testTemplate",
    "ai.token=testLlmToken"
})
@TestPropertySource(properties = {
        "ai.token=test-token"
})
@TestPropertySource(properties = {
        "ai.token=test-token",
})
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "ai.fieldInfoApi.url=http://test.url",
        "ai.token=test-token",
        "ai.fieldEnumApi.url=http://test.enum.url"
})
public class DataTableManageServiceImplTest {


    @Mock
    private FieldEncryConfigMapper fieldEncryConfigMapper;

    @Mock
    private DorisService dorisService;

    @Mock
    private AiBaseService aiBaseService;
    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Mock
    private DataPredictionService dataPredictionService;

    @Mock
    private DbConfServiceFactory dbConfServiceFactory;

    private DeleteTableRequest deleteTableRequest;

    private DataTableInfo dataTableInfo;

    private final TableFieldMetaInfo testFieldMetaInfo = new TableFieldMetaInfo();

    private final String tableName = "test_table";

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @Mock
    private DbConfService dbConfService;

    @Mock
    private WebContextHolder webContextHolder;

    @InjectMocks
    private DataTableManageServiceImpl dataTableManageService;

    private final Long dataTableId = 1L;
    private final Long dataTableId2 = 2L;

    private final String tenantId = "12345";

    private final String userId = "123";

    @BeforeEach
    void setUp() {
        deleteTableRequest = new DeleteTableRequest();
        deleteTableRequest.setDataTableId(dataTableId);

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(dataTableId);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsDel(DelEnum.NOT_DELETED.getCode());
        dataTableInfo.setDbType("DORIS");
        dataTableInfo.setTableName(tableName);

        testFieldMetaInfo.setId(1L);
        testFieldMetaInfo.setDataTableId(dataTableId);
        testFieldMetaInfo.setIsVisable(true);
        testFieldMetaInfo.setFieldTag(TableFieldTagEnum.NULL.getCode());

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(Long.valueOf(tenantId));
        userAuthInfo.setUserId(Long.valueOf(userId));
        DeepSightWebContext deepSightWebContext = new DeepSightWebContext(userAuthInfo);
        String requestId = XID.generateRequestID();
        deepSightWebContext.setRequestId(requestId);

        // 设置全局租户信息
        WebContextHolder.setDeepSightWebContext(deepSightWebContext);
    }

    @Test
    void deleteTableShouldCallDbServiceToDeleteTableWhenCalled() {
        // Arrange
        List<DataTableInfo> tableInfos = new ArrayList<>();
        tableInfos.add(dataTableInfo);
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(tableInfos);

        PredictDataSource predictDataSource = new PredictDataSource();
        predictDataSource.setDataSourceList(new ArrayList<>());
        when(dataPredictionService.getDataSourceDetail(tenantId, userId))
                .thenReturn(predictDataSource);

        when(dbConfServiceFactory.getDbService(anyString())).thenReturn(dbConfService);

        // Act
        dataTableManageService.deleteTable(deleteTableRequest);

        // Assert
        verify(dbConfService).deleteTable(tableName);
    }

    @Test
    void deleteTableShouldSuccessWhenNoDependencies() {
        // Arrange
        List<DataTableInfo> tableInfos = new ArrayList<>();
        tableInfos.add(dataTableInfo);
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(tableInfos);

        PredictDataSource predictDataSource = new PredictDataSource();
        PredictDataSet dataSource = new PredictDataSet();
        dataSource.setDatasetId(dataTableId2);
        predictDataSource.setDataSourceList(List.of(dataSource));
        when(dataPredictionService.getDataSourceDetail(tenantId, userId))
                .thenReturn(predictDataSource);

        when(dbConfServiceFactory.getDbService(anyString())).thenReturn(dbConfService);

        // Act
        assertDoesNotThrow(() -> dataTableManageService.deleteTable(deleteTableRequest));

        // Assert
        verify(dataTableInfoMapper).updateByExampleSelective(argThat(tableInfo ->
                        tableInfo.getIsDel() == DeleteStatusEnum.DELETE.getStatus().byteValue()),
                any(DataTableInfoCriteria.class));

        verify(tableFieldMetaInfoMapper).deleteByExample(any(TableFieldMetaInfoCriteria.class));
        verify(dbConfService).deleteTable(tableName);
    }

    @Test
    void deleteTableShouldThrowExceptionWhenTableNotExist() {
        // Arrange
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(new ArrayList<>());

        // Act & Assert
        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> dataTableManageService.deleteTable(deleteTableRequest));

        assertEquals(ErrorCode.NOT_FOUND, exception.getErrorCode());
        assertEquals("无此数据表", exception.getMessage());
    }

    @Test
    void deleteTableShouldThrowExceptionWhenTableHasDependencies() {
        // Arrange
        List<DataTableInfo> tableInfos = new ArrayList<>();
        tableInfos.add(dataTableInfo);
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(tableInfos);

        PredictDataSource predictDataSource = new PredictDataSource();
        PredictDataSet dataSource = new PredictDataSet();
        dataSource.setDatasetId(dataTableId);
        predictDataSource.setDataSourceList(List.of(dataSource));
        when(dataPredictionService.getDataSourceDetail(tenantId, userId))
                .thenReturn(predictDataSource);

        // Act & Assert
        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> dataTableManageService.deleteTable(deleteTableRequest));

        assertEquals(ErrorCode.FORBIDDEN_REQUEST, exception.getErrorCode());
        assertEquals("该数据集存在后置依赖-数据增强，请删除依赖后再执行此操作", exception.getMessage());
    }

    @Test
    void deleteTableShouldUpdateTableStatusWhenCalled() {
        // Arrange
        List<DataTableInfo> tableInfos = new ArrayList<>();
        tableInfos.add(dataTableInfo);
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(tableInfos);

        PredictDataSource predictDataSource = new PredictDataSource();
        predictDataSource.setDataSourceList(new ArrayList<>());
        when(dataPredictionService.getDataSourceDetail(tenantId, userId))
                .thenReturn(predictDataSource);

        when(dbConfServiceFactory.getDbService(anyString())).thenReturn(dbConfService);

        // Act
        dataTableManageService.deleteTable(deleteTableRequest);

        // Assert
        verify(dataTableInfoMapper).updateByExampleSelective(
                argThat(tableInfo -> tableInfo.getIsDel() == DeleteStatusEnum.DELETE.getStatus().byteValue()),
                any(DataTableInfoCriteria.class));
    }

    @Test
    void deleteTableShouldDeleteFieldMetaInfoWhenCalled() {
        // Arrange
        List<DataTableInfo> tableInfos = new ArrayList<>();
        tableInfos.add(dataTableInfo);
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(tableInfos);

        PredictDataSource predictDataSource = new PredictDataSource();
        predictDataSource.setDataSourceList(new ArrayList<>());
        when(dataPredictionService.getDataSourceDetail(tenantId, userId))
                .thenReturn(predictDataSource);

        when(dbConfServiceFactory.getDbService(anyString())).thenReturn(dbConfService);

        // Act
        dataTableManageService.deleteTable(deleteTableRequest);

        // Assert
        verify(tableFieldMetaInfoMapper).deleteByExample(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void getVisibleFieldsShouldReturnVisibleFieldsWhenIncludeBaiduIsTrue() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            mocked.when(WebContextHolder::getUserId).thenReturn(userId);
            // Arrange
            when(dataTableInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(new DataTableInfo()));
            when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                    .thenReturn(Collections.singletonList(testFieldMetaInfo));

            // Act
            List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(dataTableId, true);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(tableFieldMetaInfoMapper).selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class));
        }
    }

    @Test
    void getVisibleFieldsShouldReturnVisibleFieldsWhenIncludeBaiduIsFalse() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            mocked.when(WebContextHolder::getUserId).thenReturn(userId);
            // Arrange
            when(dataTableInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(new DataTableInfo()));
            when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                    .thenReturn(Collections.singletonList(testFieldMetaInfo));

            // Act
            List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(dataTableId, false);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(tableFieldMetaInfoMapper).selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class));
        }
    }

    @Test
    void getVisibleFieldsShouldReturnEmptyListWhenNoVisibleFields() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            mocked.when(WebContextHolder::getUserId).thenReturn(userId);
            // Arrange
            when(dataTableInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(new DataTableInfo()));
            when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                    .thenReturn(Collections.emptyList());

            // Act
            List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(dataTableId, true);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(tableFieldMetaInfoMapper).selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class));
        }
    }

    @Test
    void llmGenFieldEnumShouldReturnResponseWhenCalledWithValidRequest() {
        // Arrange
        GetLLMGenFieldEnumRequest request = new GetLLMGenFieldEnumRequest();
        request.setDataTableId(1L);
        request.setEnName("test_field");

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setTableName("test_table");

        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setDataType("string");
        fieldMetaInfo.setDescription("test description");

        List<Map<String, Object>> sqlRes = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("test_field", "value1");
        sqlRes.add(row);

        List<FieldEnumMappingDTO> llmRes = new ArrayList<>();
        llmRes.add(new FieldEnumMappingDTO());

        when(dataTableInfoMapper.selectByPrimaryKey(request.getDataTableId())).thenReturn(tableInfo);
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
        when(dorisService.selectList(anyString())).thenReturn(sqlRes);
        when(aiBaseService.genFieldEnum(anyString(), anyString())).thenReturn(llmRes);

        // Act
        LLMGenFieldEnumResponse response = dataTableManageService.llmGenFieldEnum(request);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getConfigInfos().size());
    }

    @Test
    void llmGenFieldEnumShouldThrowExceptionWhenFieldTypeIsJson() {
        // Arrange
        GetLLMGenFieldEnumRequest request = new GetLLMGenFieldEnumRequest();
        request.setDataTableId(1L);
        request.setEnName("test_field");

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setTableName("test_table");

        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setDataType(Constants.CONSTANT_JSON);

        when(dataTableInfoMapper.selectByPrimaryKey(request.getDataTableId())).thenReturn(tableInfo);
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));

        // Act & Assert
        ServiceException exception = assertThrows(ServiceException.class,
                () -> dataTableManageService.llmGenFieldEnum(request));
        assertEquals(ErrorCode.INTERNAL_ERROR, exception.getErrorCode());
        assertEquals("json类型不支持枚举提取", exception.getMessage());
    }

    @Test
    void llmGenFieldEnumShouldThrowExceptionWhenSqlResultIsEmpty() {
        // Arrange
        GetLLMGenFieldEnumRequest request = new GetLLMGenFieldEnumRequest();
        request.setDataTableId(1L);
        request.setEnName("test_field");

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setTableName("test_table");

        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setDataType("string");
        fieldMetaInfo.setDescription("test description");

        when(dataTableInfoMapper.selectByPrimaryKey(request.getDataTableId())).thenReturn(tableInfo);
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
        when(dorisService.selectList(anyString())).thenReturn(new ArrayList<>());

        // Act & Assert
        ServiceException exception = assertThrows(ServiceException.class,
                () -> dataTableManageService.llmGenFieldEnum(request));
        assertEquals(ErrorCode.INTERNAL_ERROR, exception.getErrorCode());
        assertEquals("数据字段为空，枚举提取失败", exception.getMessage());
    }

    @Test
    void llmGenFieldEnumShouldUseArrayDistinctWhenDataTypeIsArray() {
        // Arrange
        GetLLMGenFieldEnumRequest request = new GetLLMGenFieldEnumRequest();
        request.setDataTableId(1L);
        request.setEnName("test_field");

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setTableName("test_table");

        TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
        fieldMetaInfo.setDataType(Constants.CONSTANT_ARRAY);
        fieldMetaInfo.setDescription("test description");

        List<Map<String, Object>> sqlRes = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("test_field", "value1");
        sqlRes.add(row);

        List<FieldEnumMappingDTO> llmRes = new ArrayList<>();
        llmRes.add(new FieldEnumMappingDTO());

        when(dataTableInfoMapper.selectByPrimaryKey(request.getDataTableId())).thenReturn(tableInfo);
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(fieldMetaInfo));
        when(dorisService.selectList(anyString())).thenReturn(sqlRes);
        when(aiBaseService.genFieldEnum(anyString(), anyString())).thenReturn(llmRes);

        // Act
        LLMGenFieldEnumResponse response = dataTableManageService.llmGenFieldEnum(request);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getConfigInfos().size());
    }

    @Test
    void validDataTableByTableNameShouldReturnTableInfoWhenTableExists() {
        // Arrange
        String tableName = "test_table";
        DataTableInfo expectedTableInfo = new DataTableInfo();
        expectedTableInfo.setTableName(tableName);
        expectedTableInfo.setTenantid(tenantId);
        expectedTableInfo.setIsVisable(true);
        expectedTableInfo.setIsDel(DelEnum.NOT_DELETED.getCode());
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(List.of(expectedTableInfo));
        DataTableInfo info = dataTableManageService.validDataTableByTableName(tableName);
        assertNotNull(info);
    }

    @Test
    void validDataTableByTableNameShouldReturnNullWhenTableNotExists() {
        // Arrange
        String tableName = "non_existent_table";
        when(dataTableInfoMapper.selectByExample(any(DataTableInfoCriteria.class)))
                .thenReturn(Collections.emptyList());
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> dataTableManageService.validDataTableByTableName(tableName));
    }

    @Test
    void getVisibleFieldsShouldReturnVisibleFieldsWhenCalledWithTenantId() {
        // Arrange
        String tableName = "test_table";
        String tenantId = "12345";
        boolean includeBaidu = false;

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setTableName(tableName);

        TableFieldMetaInfo field1 = new TableFieldMetaInfo();
        field1.setId(1L);
        field1.setTableEnName(tableName);
        field1.setIsVisable(true);
        field1.setFromBaidu(true);
        field1.setFieldTag(0);

        TableFieldMetaInfo field2 = new TableFieldMetaInfo();
        field2.setId(2L);
        field2.setTableEnName(tableName);
        field2.setIsVisable(true);
        field2.setFromBaidu(false);
        field2.setFieldTag(0);

        List<TableFieldMetaInfo> mockFields = List.of(field1, field2);

        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(List.of(dataTableInfo));
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(mockFields);

        // Act
        List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(tableName, tenantId, includeBaidu);

        // Assert
        assertEquals(2, result.size());
        verify(dataTableInfoMapper).selectByExampleWithBLOBs(any(DataTableInfoCriteria.class));
        verify(tableFieldMetaInfoMapper).selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void getVisibleFieldsShouldReturnVisibleFieldsWhenCalledWithoutTenantId() {
        // Arrange
        String tableName = "test_table";
        boolean includeBaidu = false;

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setTableName(tableName);

        TableFieldMetaInfo field1 = new TableFieldMetaInfo();
        field1.setId(1L);
        field1.setTableEnName("tableName");
        field1.setFieldTag(0);

        List<TableFieldMetaInfo> mockFields = List.of(field1);
        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(List.of(dataTableInfo));
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(mockFields);

        // Act
        List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(tableName, null, includeBaidu);

        // Assert
        assertEquals(1, result.size());
        verify(dataTableInfoMapper, times(1)).selectByExampleWithBLOBs(any(DataTableInfoCriteria.class));
        verify(tableFieldMetaInfoMapper).selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void getVisibleFieldsShouldExcludeBaiduFieldsWhenIncludeBaiduIsFalse() {
        // Arrange
        String tableName = "test_table";
        String tenantId = "12345";
        boolean includeBaidu = false;

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setTableName(tableName);

        TableFieldMetaInfo field1 = new TableFieldMetaInfo();
        field1.setId(1L);
        field1.setTableEnName(tableName);
        field1.setIsVisable(true);
        field1.setFromBaidu(true);
        field1.setFieldTag(0);

        TableFieldMetaInfo field2 = new TableFieldMetaInfo();
        field2.setId(2L);
        field2.setTableEnName(tableName);
        field2.setIsVisable(true);
        field2.setFromBaidu(false);
        field2.setFieldTag(0);

        List<TableFieldMetaInfo> mockFields = List.of(field1, field2);

        when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                .thenReturn(List.of(dataTableInfo));
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(mockFields);

        // Act
        List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(tableName, tenantId, includeBaidu);

        // Assert
        verify(dataTableInfoMapper).selectByExampleWithBLOBs(any(DataTableInfoCriteria.class));
        verify(tableFieldMetaInfoMapper).selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void getVisibleFieldsShouldThrowExceptionWhenTableNotFound() {
        // Arrange
        String tableName = "non_existent_table";
        boolean includeBaidu = true;

        when(dataTableInfoMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            dataTableManageService.getVisibleFields(tableName, includeBaidu);
        });
    }

    @Test
    void getVisibleFieldsShouldReturnVisibleFieldsWhenCalledWithTableName() {
        // Arrange
        String tableName = "test_table";
        boolean includeBaidu = true;

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(dataTableId);
        dataTableInfo.setTenantid(tenantId);

        List<TableFieldMetaInfo> fieldMetaInfos = new ArrayList<>();
        TableFieldMetaInfo visibleField = new TableFieldMetaInfo();
        visibleField.setId(1L);
        visibleField.setDataTableId(dataTableId);
        visibleField.setIsVisable(true);
        visibleField.setEnField("visible_field");
        visibleField.setFieldTag(0);
        fieldMetaInfos.add(visibleField);

        TableFieldMetaInfo hiddenField = new TableFieldMetaInfo();
        hiddenField.setId(2L);
        hiddenField.setDataTableId(dataTableId);
        hiddenField.setIsVisable(false);
        hiddenField.setEnField("hidden_field");
        hiddenField.setFieldTag(0);
        fieldMetaInfos.add(hiddenField);

        when(dataTableInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(dataTableInfo));
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(fieldMetaInfos);

        // Act
        List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(tableName, includeBaidu);

        // Assert
        assertEquals("visible_field", result.get(0).getEnName());
    }

    @Test
    void getVisibleFieldsShouldReturnEmptyListWhenNoVisibleFieldsFound() {
        // Arrange
        String tableName = "test_table";
        boolean includeBaidu = true;

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(dataTableId);
        dataTableInfo.setTenantid(tenantId);

        List<TableFieldMetaInfo> fieldMetaInfos = new ArrayList<>();
        TableFieldMetaInfo hiddenField = new TableFieldMetaInfo();
        hiddenField.setId(1L);
        hiddenField.setDataTableId(dataTableId);
        hiddenField.setIsVisable(false);
        hiddenField.setEnField("hidden_field");
        fieldMetaInfos.add(hiddenField);

        when(dataTableInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(dataTableInfo));
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of());

        // Act
        List<VisibleFieldResponse> result = dataTableManageService.getVisibleFields(tableName, includeBaidu);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @Test
    void getTableFieldListShouldReturnFieldsForUserTableType() {
        // Arrange
        GetTableFieldRequest request = new GetTableFieldRequest();
        request.setTableType("user");
        request.setPageNo(1);
        request.setPageSize(10);

        List<TableFieldMetaInfo> mockFields = new ArrayList<>();
        testFieldMetaInfo.setTableEnName("mock_user_" + tenantId);
        mockFields.add(testFieldMetaInfo);

        when(tableFieldMetaInfoMapper.countByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(1L);
        when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(mockFields);

        // Act
        BasePageResponse.Page<TableFieldDetailResponse> result =
                dataTableManageService.getTableFieldList(request, true);

        // Assert
        assertEquals(1, result.getPageNo());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
    }

    @Test
    void getTableFieldByEnNameShouldReturnFieldMetaInfoWhenFieldExists() {
        // Arrange
        Long dataTableId = 1L;
        String enName = "test_field";
        TableFieldMetaInfo expectedField = new TableFieldMetaInfo();
        expectedField.setId(1L);
        expectedField.setDataTableId(dataTableId);
        expectedField.setEnField(enName);

        List<TableFieldMetaInfo> fieldList = new ArrayList<>();
        fieldList.add(expectedField);

        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(fieldList);

        // Act
        TableFieldMetaInfo result = dataTableManageService.getTableFieldByEnName(dataTableId, enName);

        // Assert
        assertNotNull(result);
        assertEquals(expectedField.getId(), result.getId());
        assertEquals(expectedField.getDataTableId(), result.getDataTableId());
        assertEquals(expectedField.getEnField(), result.getEnField());
        verify(tableFieldMetaInfoMapper).selectByExample(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void getTableFieldByEnNameShouldReturnNullWhenFieldNotExists() {
        // Arrange
        Long dataTableId = 1L;
        String enName = "non_existent_field";

        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(new ArrayList<>());

        // Act
        TableFieldMetaInfo result = dataTableManageService.getTableFieldByEnName(dataTableId, enName);

        // Assert
        assertNull(result);
        verify(tableFieldMetaInfoMapper).selectByExample(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void getTableUserInfoShouldReturnEmptyPageWhenNoTableFound() {
    // Arrange
    GetTableUserInfoRequest request = new GetTableUserInfoRequest();
    request.setUserFieldName("testField");
    request.setUserFieldValue("testValue");

    when(dataTableInfoMapper.countByExample(any(DataTableInfoCriteria.class))).thenReturn(0L);

    // Act
    BasePageResponse.Page<Map<String, String>> result =
        dataTableManageService.getTableUserInfo(false, request);

    // Assert
    assertEquals(0L, result.getTotal());
    assertTrue(result.getResults().isEmpty());
    }

    @Test
    void getTableUserInfoShouldReturnEmptyPageWhenTableFoundButNoField() {
    // Arrange
    GetTableUserInfoRequest request = new GetTableUserInfoRequest();
    request.setUserFieldName("testField");
    request.setUserFieldValue("testValue");

    List<DataTableInfo> tableInfos = new ArrayList<>();
    tableInfos.add(dataTableInfo);

    when(dataTableInfoMapper.countByExample(any(DataTableInfoCriteria.class))).thenReturn(1L);
    when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class))).thenReturn(tableInfos);
    when(tableFieldMetaInfoMapper.countByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(0L);

    // Act
    BasePageResponse.Page<Map<String, String>> result =
        dataTableManageService.getTableUserInfo(false, request);

    // Assert
    assertEquals(0L, result.getTotal());
    assertTrue(result.getResults().isEmpty());
    }

    @Test
    void getTableUserInfoShouldReturnSearchResultWhenTableAndFieldExist() {
    // Arrange
    GetTableUserInfoRequest request = new GetTableUserInfoRequest();
    request.setUserFieldName("testField");
    request.setUserFieldValue("testValue");
    request.setPageNo(1);
    request.setPageSize(10);

    List<DataTableInfo> tableInfos = new ArrayList<>();
    tableInfos.add(dataTableInfo);

    TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
    fieldMetaInfo.setId(1L);
    fieldMetaInfo.setDataTableId(dataTableId);
    fieldMetaInfo.setEnField(request.getUserFieldName());

    List<TableFieldMetaInfo> fieldInfos = new ArrayList<>();
    fieldInfos.add(fieldMetaInfo);

    BasePageResponse.Page<Map<String, String>> expectedResult =
        BasePageResponse.Page.of(1, 10, 1L, new ArrayList<>());

    when(dataTableInfoMapper.countByExample(any(DataTableInfoCriteria.class))).thenReturn(1L);
    when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class))).thenReturn(tableInfos);
    when(tableFieldMetaInfoMapper.countByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(1L);
    when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
        .thenReturn(fieldInfos);

    DbConfService mockDbService = Mockito.mock(DbConfService.class);
    when(dbConfServiceFactory.getDbService(anyString())).thenReturn(mockDbService);
    when(mockDbService.search(any(GetTableContentListRequest.class), anyBoolean()))
        .thenReturn(expectedResult);

    // Act
    BasePageResponse.Page<Map<String, String>> result =
        dataTableManageService.getTableUserInfo(false, request);

    // Assert
//    assertEquals(expectedResult, result);
//    verify(mockDbService).search(any(GetTableContentListRequest.class), eq(false));
    }

    @Test
    void getTableUserInfoShouldReturnEmptyPageWhenFieldFoundButNoData() {
    // Arrange
    GetTableUserInfoRequest request = new GetTableUserInfoRequest();
    request.setUserFieldName("testField");
    request.setUserFieldValue("testValue");

    List<DataTableInfo> tableInfos = new ArrayList<>();
    tableInfos.add(dataTableInfo);

    TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
    fieldMetaInfo.setId(1L);
    fieldMetaInfo.setDataTableId(dataTableId);
    fieldMetaInfo.setEnField(request.getUserFieldName());

    List<TableFieldMetaInfo> fieldInfos = new ArrayList<>();
    fieldInfos.add(fieldMetaInfo);

    when(dataTableInfoMapper.countByExample(any(DataTableInfoCriteria.class))).thenReturn(1L);
    when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class))).thenReturn(tableInfos);
    when(tableFieldMetaInfoMapper.countByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(1L);
    when(tableFieldMetaInfoMapper.selectByExampleWithBLOBs(any(TableFieldMetaInfoCriteria.class)))
        .thenReturn(fieldInfos);

    DbConfService mockDbService = Mockito.mock(DbConfService.class);
    when(dbConfServiceFactory.getDbService(anyString())).thenReturn(mockDbService);
    when(mockDbService.search(any(GetTableContentListRequest.class), anyBoolean()))
        .thenReturn(BasePageResponse.Page.of(1, 10, 0L, new ArrayList<>()));

    // Act
    BasePageResponse.Page<Map<String, String>> result =
        dataTableManageService.getTableUserInfo(false, request);

    // Assert
    assertEquals(0L, result.getTotal());
    assertTrue(result.getResults().isEmpty());
    }

    // testGetTablePropertiesNoDataTable 用于测试 getTableProperties
    // generated by Comate
    @Test
    public void testGetTablePropertiesNoDataTable() {
        deleteTableRequest = new DeleteTableRequest();
        deleteTableRequest.setDataTableId(dataTableId);
        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(dataTableId);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsDel(DelEnum.NOT_DELETED.getCode());
        dataTableInfo.setDbType("DORIS");
        dataTableInfo.setTableName(tableName);
        testFieldMetaInfo.setId(1L);
        testFieldMetaInfo.setDataTableId(dataTableId);
        testFieldMetaInfo.setIsVisable(true);
        testFieldMetaInfo.setFieldTag(TableFieldTagEnum.NULL.getCode());
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(Long.valueOf(tenantId));
        userAuthInfo.setUserId(Long.valueOf(userId));
        DeepSightWebContext deepSightWebContext = new DeepSightWebContext(userAuthInfo);
        String requestId = XID.generateRequestID();
        deepSightWebContext.setRequestId(requestId);
        WebContextHolder.setDeepSightWebContext(deepSightWebContext);

        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class)) {
            mockedWebContextHolder.when(WebContextHolder::getTenantId).thenReturn("testTenantId");
            when(dataTableInfoMapper.selectByExampleWithBLOBs(any(DataTableInfoCriteria.class)))
                    .thenReturn(new ArrayList<>());
            List<DatasetPropertiesResult> results = dataTableManageService.getTableProperties();
            assertTrue(results.isEmpty(), "Expected an empty list of DatasetPropertiesResult");
        }
    }

    @Test
    void llmGenFieldInfoV1ShouldReturnFieldInfoWithCorrectProperties() {
        // Arrange
        GetLLMGenTableInfoRequest request = new GetLLMGenTableInfoRequest();
        request.setPromptContent("test prompt");

        List<TableFieldInfoDTO> mockFieldInfos = new ArrayList<>();
        TableFieldInfoDTO field1 = new TableFieldInfoDTO();
        field1.setEnName("field1");
        field1.setCnName("字段1");
        field1.setDescription("测试字段1");
        mockFieldInfos.add(field1);

        when(aiBaseService.genFieldInfo(anyString(), anyString()))
                .thenReturn(mockFieldInfos);

        // Act
        LLMGenFieldResponse response = dataTableManageService.llmGenFieldInfoV1(request);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getTableFieldInfos());
        assertEquals(1, response.getTableFieldInfos().size());

        TableFieldInfoDTO resultField = response.getTableFieldInfos().get(0);
        assertEquals("field1", resultField.getEnName());
        assertEquals("字段1", resultField.getCnName());
        assertEquals("测试字段1", resultField.getDescription());
        assertTrue(resultField.getIsShowValue());
        assertTrue(resultField.getIsRequired());
        assertEquals(TableFieldTagEnum.NULL.getCode(), resultField.getFieldTag());
        assertTrue(resultField.getIsFilterCriteria());
        assertEquals("text", resultField.getValueType());
        assertEquals("varchar", resultField.getDataType());

        verify(aiBaseService).genFieldInfo("test prompt", "tableFieldInfos");
    }

    @Test
    void llmGenFieldInfoV1ShouldHandleEmptyFieldList() {
        // Arrange
        GetLLMGenTableInfoRequest request = new GetLLMGenTableInfoRequest();
        request.setPromptContent("empty test prompt");

        when(aiBaseService.genFieldInfo(anyString(), anyString()))
                .thenReturn(new ArrayList<>());

        // Act
        LLMGenFieldResponse response = dataTableManageService.llmGenFieldInfoV1(request);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getTableFieldInfos());
        assertTrue(response.getTableFieldInfos().isEmpty());

        verify(aiBaseService).genFieldInfo("empty test prompt", "tableFieldInfos");
    }

    @Test
    void llmGenFieldInfoV1ShouldSetDefaultPropertiesForAllFields() {
        // Arrange
        GetLLMGenTableInfoRequest request = new GetLLMGenTableInfoRequest();
        request.setPromptContent("multi fields prompt");

        List<TableFieldInfoDTO> mockFieldInfos = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            TableFieldInfoDTO field = new TableFieldInfoDTO();
            field.setEnName("field" + i);
            mockFieldInfos.add(field);
        }

        when(aiBaseService.genFieldInfo(anyString(), anyString()))
                .thenReturn(mockFieldInfos);

        // Act
        LLMGenFieldResponse response = dataTableManageService.llmGenFieldInfoV1(request);

        // Assert
        assertNotNull(response);
        assertEquals(3, response.getTableFieldInfos().size());

        for (TableFieldInfoDTO field : response.getTableFieldInfos()) {
            assertTrue(field.getIsShowValue());
            assertTrue(field.getIsRequired());
            assertEquals(TableFieldTagEnum.NULL.getCode(), field.getFieldTag());
            assertTrue(field.getIsFilterCriteria());
            assertEquals("text", field.getValueType());
            assertEquals("varchar", field.getDataType());
        }

        verify(aiBaseService).genFieldInfo("multi fields prompt", "tableFieldInfos");
    }

    // testGetTableEncryFieldsNoResults 用于测试 getTableEncryFields
    // generated by Comate
    @Test
    public void testGetTableEncryFieldsNoResults() {
        deleteTableRequest = new DeleteTableRequest();
        deleteTableRequest.setDataTableId(dataTableId);
        dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(dataTableId);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsDel(DelEnum.NOT_DELETED.getCode());
        dataTableInfo.setDbType("DORIS");
        dataTableInfo.setTableName(tableName);
        testFieldMetaInfo.setId(1L);
        testFieldMetaInfo.setDataTableId(dataTableId);
        testFieldMetaInfo.setIsVisable(true);
        testFieldMetaInfo.setFieldTag(TableFieldTagEnum.NULL.getCode());
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(Long.valueOf(tenantId));
        userAuthInfo.setUserId(Long.valueOf(userId));
        DeepSightWebContext deepSightWebContext = new DeepSightWebContext(userAuthInfo);
        String requestId = XID.generateRequestID();
        deepSightWebContext.setRequestId(requestId);
        WebContextHolder.setDeepSightWebContext(deepSightWebContext);
    
        MockitoAnnotations.openMocks(this);
        Long dataTableId = 1L;
        FieldEncryConfigCriteria criteria = new FieldEncryConfigCriteria();
        FieldEncryConfigCriteria.Criteria fieldCriteria = criteria.createCriteria();
        fieldCriteria.andDataTableIdEqualTo(Long.toString(dataTableId));
        when(fieldEncryConfigMapper.selectByExample(any(FieldEncryConfigCriteria.class))).thenReturn(Collections.emptyList());
        List<String> result = dataTableManageService.getTableEncryFields(dataTableId);
        assertEquals(0, result.size());
        verify(fieldEncryConfigMapper, times(1)).selectByExample(any(FieldEncryConfigCriteria.class));
    }


}