package com.baidu.keyue.deepsight.config.threadpool;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 线程池配置类
 */
@Configuration
public class ExecutorConfig {

    @Bean(name = "manualCalExecutor")
    public ExecutorService fetchManualCalExecutor(
            @Value("${manualCalExecutor.coreNumber:10}") int threadCorePoolSize,
            @Value("${manualCalExecutor.maxNumber:20}") int threadMaxPoolSize,
            @Value("${manualCalExecutor.queueSize:1000}") int threadWorkQueueSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadCorePoolSize, threadMaxPoolSize, 5000L, TimeUnit.MICROSECONDS,
                new ArrayBlockingQueue<>(threadWorkQueueSize),
                new NamedThreadFactory("manualCalExecutor-EXECUTOR"),
                new ThreadPoolExecutor.AbortPolicy()
        );
        return executor;
    }

    @Bean(name = "labelCalExecutor")
    public ExecutorService fetchLabelCalExecutor(
            @Value("${labelCalExecutor.coreNumber:10}") int threadCorePoolSize,
            @Value("${labelCalExecutor.maxNumber:20}") int threadMaxPoolSize,
            @Value("${labelCalExecutor.queueSize:1000}") int threadWorkQueueSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadCorePoolSize, threadMaxPoolSize, 5000L, TimeUnit.MICROSECONDS,
                new ArrayBlockingQueue<>(threadWorkQueueSize),
                new NamedThreadFactory("labelCalExecutor-EXECUTOR"),
                new ThreadPoolExecutor.AbortPolicy()
        );
        return executor;
    }

    @Bean(name = "customerGroupCalExecutor")
    public ExecutorService fetchCustomerGroupCalExecutor(
            @Value("${customerGroupCal.coreNumber:10}") int threadCorePoolSize,
            @Value("${customerGroupCal.maxNumber:20}") int threadMaxPoolSize,
            @Value("${customerGroupCal.queueSize:1000}") int threadWorkQueueSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadCorePoolSize, threadMaxPoolSize, 5000L, TimeUnit.MICROSECONDS,
                new ArrayBlockingQueue<>(threadWorkQueueSize),
                new NamedThreadFactory("customerGroupCal-EXECUTOR"),
                new ThreadPoolExecutor.AbortPolicy()
        );
        return executor;
    }

    @Bean(name = "customerGroupExecutor")
    public ExecutorService fetchCustomerGroupExecutor(
            @Value("${customerGroup.coreNumber:10}") int threadCorePoolSize,
            @Value("${customerGroup.maxNumber:20}") int threadMaxPoolSize,
            @Value("${customerGroup.queueSize:1000}") int threadWorkQueueSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadCorePoolSize, threadMaxPoolSize, 5000L, TimeUnit.MICROSECONDS,
                new ArrayBlockingQueue<>(threadWorkQueueSize),
                new NamedThreadFactory("customerGroup-EXECUTOR"),
                new ThreadPoolExecutor.AbortPolicy()
        );
        return executor;
    }

    @Bean(name = "tableContentExecutor")
    public ExecutorService fetchTableContentExecutor(
            @Value("${tableContent.coreNumber:10}") int threadCorePoolSize,
            @Value("${tableContent.maxNumber:20}") int threadMaxPoolSize,
            @Value("${tableContent.queueSize:1000}") int threadWorkQueueSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadCorePoolSize, threadMaxPoolSize, 5000L, TimeUnit.MICROSECONDS,
                new ArrayBlockingQueue<>(threadWorkQueueSize),
                new NamedThreadFactory("tableContent-EXECUTOR"),
                new ThreadPoolExecutor.AbortPolicy()
        );
        return executor;
    }


    @Bean(name = "aiobSOPExecutor")
    public ExecutorService fetchAiobSOPExecutor(
            @Value("${aiobSOP.coreNumber:10}") int threadCorePoolSize,
            @Value("${aiobSOP.maxNumber:20}") int threadMaxPoolSize,
            @Value("${aiobSOP.queueSize:1000}") int threadWorkQueueSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadCorePoolSize, threadMaxPoolSize, 5000L, TimeUnit.MICROSECONDS,
                new ArrayBlockingQueue<>(threadWorkQueueSize),
                new NamedThreadFactory("aiobSOP-EXECUTOR"),
                new ThreadPoolExecutor.AbortPolicy()
        );
        return executor;
    }


    private static class NamedThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(0);

        public NamedThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            return thread;
        }
    }

}
