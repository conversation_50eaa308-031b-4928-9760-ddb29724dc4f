package com.baidu.keyue.deepsight.utils;

import java.io.UnsupportedEncodingException;
import java.security.SecureRandom;
import java.util.UUID;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @className AESUtils
 * @description aes加解密
 * @date 2025/1/6 17:19
 */
@Slf4j
public class AESUtils {
    /**
     * 密钥长度: 128, 192 or 256
     */
    private static final int KEY_SIZE = 128;

    /**
     * 加密/解密算法名称
     */
    private static final String AES = "AES";
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";

    /**
     * 随机数生成器（RNG）算法名称
     */
    private static final String RANDOM_ALGORITHM = "SHA1PRNG";

    public static final String CHARSET = "utf-8";

    /**
     * 数据加密: 明文 -> 密文
     * 加解密的密码，JAVA中有效密码为16位/24位/32位
     *
     * @param plainText
     * @param password
     * @return 加密后的字符串
     * @throws Exception
     */
    public static String encryptAES(String plainText, String password) throws UnsupportedEncodingException {
//        log.info("加密明文：{}, 密钥：{}", plainText, password);
        String cipherText = plainText;
        if (StringUtils.isNotBlank(plainText) && StringUtils.isNotBlank(password)) {
            byte[] cipherBytes = encryptAES(plainText.getBytes(CHARSET), password.getBytes(CHARSET));
            cipherText = byte2HexText(cipherBytes);
        }
//        log.info("加密明文：{}, 密钥：{}， 结果：{}", plainText, password, cipherText);

        return cipherText;
    }

    /**
     * 数据加密: 明文 -> 密文
     *
     * @param plainBytes
     * @param password
     * @return 加密后的字节数组
     * @throws Exception
     */
    private static byte[] encryptAES(byte[] plainBytes, byte[] password) {
        byte[] cipherBytes = null;
        try {
            if (plainBytes != null && password != null) {
                SecretKeySpec skeySpec = new SecretKeySpec(password, AES);
                Cipher cipher = Cipher.getInstance(ALGORITHM);
                cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
                cipher.init(Cipher.ENCRYPT_MODE, skeySpec);

                cipherBytes = cipher.doFinal(plainBytes);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //            throw new BusinessException(e.getMessage(), e);
            throw new RuntimeException();
        }

        return cipherBytes;
    }

    /**
     * 数据解密: 密文 -> 明文
     * 加解密的密码，JAVA中有效密码为16位/24位/32位
     *
     * @param cipherText
     * @param password
     * @return 解密后的字符串
     */
    @SneakyThrows
    public static String decryptAES(String cipherText, String password) {
        log.info("解密明文：{}, 密钥：{}", cipherText, password);
        String plainText = cipherText;
        if (StringUtils.isNotBlank(cipherText) && StringUtils.isNotBlank(password)) {
            byte[] plainBytes = decryptAES(hex2byte(cipherText), password.getBytes(CHARSET));
            plainText = new String(plainBytes);
        }
        log.info("解密明文：{}, 密钥：{}, 结果：{}", cipherText, password, plainText);
        return plainText;
    }

    /**
     * 数据解密: 密文 -> 明文
     *
     * @param cipherBytes
     * @param password
     * @return 解密后的字节数组
     * @throws Exception
     */
    private static byte[] decryptAES(byte[] cipherBytes, byte[] password) {
        byte[] plainBytes = null;
        try {
            if (cipherBytes != null && password != null) {
                SecretKeySpec skeySpec = new SecretKeySpec(password, AES);
                Cipher cipher = Cipher.getInstance(ALGORITHM);
                cipher.init(Cipher.DECRYPT_MODE, skeySpec);
                plainBytes = cipher.doFinal(cipherBytes);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException();
        }

        log.info("decrypt plainBytes length:{}", plainBytes == null ? 0 : plainBytes.length);
        return plainBytes;
    }

    public static String encrypt(String plainText, String password) {
        String cipherText = plainText;
        if (StringUtils.isNotBlank(plainText) && StringUtils.isNotBlank(password)) {
            byte[] cipherBytes = encrypt(plainText.getBytes(), password.getBytes());
            cipherText = byte2HexText(cipherBytes);
        }
        return cipherText;
    }

    private static byte[] encrypt(byte[] plainBytes, byte[] password) {
        byte[] cipherBytes = null;

        try {
            if (plainBytes != null && password != null) {
                SecretKey secKey = generateKey(password);
                Cipher cipher = Cipher.getInstance("AES");
                cipher.init(1, secKey);
                cipherBytes = cipher.doFinal(plainBytes);
            }

            return cipherBytes;
        } catch (Exception var5) {
            throw new RuntimeException();
        }
    }

    public static String decrypt(String cipherText, String password) {
        String plainText = cipherText;
        if (StringUtils.isNotBlank(cipherText) && StringUtils.isNotBlank(password)) {
            byte[] plainBytes = decrypt(hex2byte(cipherText), password.getBytes());
            plainText = new String(plainBytes);
        }
        return plainText;
    }

    private static byte[] decrypt(byte[] cipherBytes, byte[] password) {
        byte[] plainBytes = null;

        try {
            if (cipherBytes != null && password != null) {
                SecretKey secKey = generateKey(password);
                Cipher cipher = Cipher.getInstance(AES);
                cipher.init(2, secKey);
                plainBytes = cipher.doFinal(cipherBytes);
            }

            return plainBytes;
        } catch (Exception var5) {
            throw new RuntimeException();
        }
    }

    private static SecretKey generateKey(byte[] key) {
        KeyGenerator keyGenerator = null;

        try {
            SecureRandom random = SecureRandom.getInstance(RANDOM_ALGORITHM);
            random.setSeed(key);
            keyGenerator = KeyGenerator.getInstance(AES);
            keyGenerator.init(128, random);
        } catch (Exception var3) {
            throw new RuntimeException();
        }

        return keyGenerator.generateKey();
    }

    /**
     * 二进制转换成字节数组，为了解决密文字节数组不能直接转成字符串
     *
     * @param hexText
     * @return 字节数组
     */
    private static byte[] hex2byte(String hexText) {
        if (hexText == null) {
            return null;
        }
        int l = hexText.length();
        if (l % 2 == 1) {
            return null;
        }
        byte[] b = new byte[l / 2];
        for (int i = 0; i != l / 2; i++) {
            b[i] = (byte) Integer.parseInt(hexText.substring(i * 2, i * 2 + 2),
                    16);
        }
        return b;
    }
    public static  String generatorAesPassword() {
        String uuid = UUID.randomUUID().toString().replaceAll("-", ""); // 去掉连字符
        String randomString = uuid.substring(0, 16);
        return randomString;

    }

    /**
     * 字节数组转成二进制字符串，为了解决密文字节数组不能直接转成字符串
     *
     * @param bytes
     * @return 字符串
     */
    private static String byte2HexText(byte[] bytes) {
        String hexText = "";
        String strTmp = "";
        for (int n = 0; n < bytes.length; n++) {
            strTmp = (Integer.toHexString(bytes[n] & 0XFF));
            if (strTmp.length() == 1) {
                hexText = hexText + "0" + strTmp;
            } else {
                hexText = hexText + strTmp;
            }
        }
        return hexText.toUpperCase();
    }
    public static void main(String[] args) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", ""); // 去掉连字符
        String randomString = uuid.substring(0, 16); // 截取前16个字符
        System.out.println("Generated Random String from UUID: " + randomString);
    }
}
