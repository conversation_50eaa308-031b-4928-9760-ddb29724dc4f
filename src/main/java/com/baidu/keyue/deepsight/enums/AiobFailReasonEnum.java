package com.baidu.keyue.deepsight.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * @ClassName AiobNotConnectedEnum
 * @Description 外呼session未接通原因枚举
 * <AUTHOR>
 * @Date 2025/6/27 2:59 PM
 */
@Getter
public enum AiobFailReasonEnum {
    // 号线原因未接通
    CANT_BE_CONNECTED(AiobFailTypeEnum.LINE, "无法接通", "cantbeconnected", null, 5),
    CALLER_MATCH_FAIL(AiobFailTypeEnum.LINE, "主叫号码超频限制", "callermatchfail", null, 76),
    CALLER_ARREARS1(AiobFailTypeEnum.LINE, "主叫欠费", null, "438", null),
    CALLER_ARREARS2(AiobFailTypeEnum.LINE, "主叫欠费", null, "466", null),
    CALL_FAILED(AiobFailTypeEnum.LINE, "呼叫失败", null, "503", null),
    NO_LINE_AVAILABLE(AiobFailTypeEnum.LINE, "无可用线路", null, "434", null),
    NO_CALLER_AVAILABLE(AiobFailTypeEnum.LINE, "无可用主叫", null, "435", null),
    LINE_FAULT(AiobFailTypeEnum.LINE, "线路故障", null, "469", null),
//    OTHER_REASON(AiobFailTypeEnum.LINE, "其他原因", "else", null, 99),
    // 平台规则限制未接通
    BLACK_LIST(AiobFailTypeEnum.PLATFORM_RULE, "黑名单", "blacklist", null, 1),
    RULES_LIMIT(AiobFailTypeEnum.PLATFORM_RULE, "规则限拨", "ruleslimit", null, 20),
    BEYOND_DEADLINE(AiobFailTypeEnum.PLATFORM_RULE, "超过截止时间", "beyonddeadline", null, 21),
    NO_CALL_PHONE_400(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-400号码", "nocallphone400", null, 50),
    NO_CALL_PHONE_800(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-800号码", "nocallphone800", null, 51),
    NO_CALL_FIXED_PHONE(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-固话", "nocallfixedphone", null, 52),
    NO_CALL_TELE_PHONE(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-手机号码", "nocalltelephone", null, 53),
    NO_CALL_PHONE_95(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-95号码", "nocallphone95", null, 54),
    NO_CALL_PHONE_96(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-96号码", "nocallphone96", null, 55),
    NO_CALL_PHONE_ELSE(AiobFailTypeEnum.PLATFORM_RULE, "禁拨-其他", "nocallphoneelse", null, 56),
    TASK_NOT_DIAL(AiobFailTypeEnum.PLATFORM_RULE, "本任务不再拨打", "tasknotdial", null, 70),
    MOBILE_REPEAT(AiobFailTypeEnum.PLATFORM_RULE, "号码重复过滤", "mobilerepeat", null, 71),
    MEMBER_EXIST_BLACK_LIST(AiobFailTypeEnum.PLATFORM_RULE, "号码组存在黑名单", "memberexistblacklist", null, 72),
    FORBIDDEN_NUM(AiobFailTypeEnum.PLATFORM_RULE, "自定义禁呼号码", "forbiddennum", null, 73),
    MEMBER_EXIST_FORBIDDEN_NUM(AiobFailTypeEnum.PLATFORM_RULE, "号码组存在自定义禁呼号码", "memberexistforbiddennum", null, 74),
    CUSTOM_FORBID_DURATION_REPEAT(AiobFailTypeEnum.PLATFORM_RULE, "60天内已拨打", "customForbidDurationRepeat", null, 77),
    // 被叫原因未接通
    NOT_EXIST(AiobFailTypeEnum.CALLED_UP, "空号", "notexist", null, 2),
    STOPPED(AiobFailTypeEnum.CALLED_UP, "停机", "stopped", null, 3),
    POWER_OFF(AiobFailTypeEnum.CALLED_UP, "关机", "poweroff", null, 4),
    LINE_BUSY(AiobFailTypeEnum.CALLED_UP, "用户忙", "linebusy", null, 6),
    NO_ONE_HEARD(AiobFailTypeEnum.CALLED_UP, "无人接听", "nooneheard", null, 7),
    RECV_REFUSE(AiobFailTypeEnum.CALLED_UP, "拒接（振铃后挂断）", null, null, null, "recv_refuse"),
    ;
    private final AiobFailTypeEnum typeCategory;
    private final String typeName;
    private final String endTypeReason;
    private final String sipCode;
    private final Integer dicId;
    private final String hangupReason;

    /**
     * 拒接类型忽略的code集合
     */
    private static Set<String> ignoreCodes = Set.of("notexist", "stopped", "poweroff", "linebusy", "nooneheard", "cantbeconnected");


    AiobFailReasonEnum(AiobFailTypeEnum typeCategory, String typeName, String endTypeReason, String sipCode, Integer dicId) {
        this.typeCategory = typeCategory;
        this.typeName = typeName;
        this.endTypeReason = endTypeReason;
        this.sipCode = sipCode;
        this.dicId = dicId;
        this.hangupReason = null;
    }

    AiobFailReasonEnum(AiobFailTypeEnum typeCategory, String typeName, String endTypeReason, String sipCode, Integer dicId, String hangupReason) {
        this.typeCategory = typeCategory;
        this.typeName = typeName;
        this.endTypeReason = endTypeReason;
        this.sipCode = sipCode;
        this.dicId = dicId;
        this.hangupReason = hangupReason;
    }

    public static AiobFailReasonEnum create(String endTypeReason, String sipCode, String hangupReason) {
        for (AiobFailReasonEnum reasonEnum : values()) {
            // 如果是振铃后挂断拒接，不能与被叫原因未接通的code冲突，且不能为else
            if (Objects.equals(RECV_REFUSE.getHangupReason(), hangupReason) && !ignoreCodes.contains(endTypeReason)) {
                return RECV_REFUSE;
            }
            // 如果是其他类型，按照endTypeReason和sipCode匹配
            if ((StrUtil.isNotBlank(endTypeReason) && Objects.equals(endTypeReason, reasonEnum.getEndTypeReason()))
                    || (StrUtil.isNotBlank(sipCode) && Objects.equals(sipCode, reasonEnum.getSipCode()))) {
                return reasonEnum;
            }
        }
        return null;
    }

    public static AiobFailReasonEnum createByTypeName(String typeName) {
        for (AiobFailReasonEnum reasonEnum : values()) {
            if ((StrUtil.isNotBlank(typeName) && Objects.equals(typeName, reasonEnum.getTypeName()))) {
                return reasonEnum;
            }
        }
        return null;
    }
}
