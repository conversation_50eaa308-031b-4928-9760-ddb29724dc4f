package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @className TableContentReq
 * @description 数据表内容记录请求
 * @date 2024/12/25 17:33
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTableContentListRequest extends BasePageRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "dataTableId不能为空")
    private Long dataTableId;

    /**
     * 过滤条件
     */
    private List<RuleFilter> filters;

    /**
     * 定义可以获取明文的字段名称
     * @param fileId
     * @param praValue
     * @param dataTableId
     * @return
     */
    private Set<String> decryptedOutputField;


    public static GetTableContentListRequest buildExternalRequest(Long fileId, String praValue, Long dataTableId) {
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(fileId);
        filter.setType(FilterTypeEnum.STRING);
        filter.setFunction(FuncEnum.CONTAIN);
        filter.setParams(Collections.singletonList(praValue));

        GetTableContentListRequest request = new GetTableContentListRequest();
        request.setDataTableId(dataTableId);
        request.setPageNo(1);
        request.setPageSize(1);
        request.setFilters(Collections.singletonList(filter));
        request.setDecryptedOutputField(Collections.singleton(Constants.TABLE_MOBILE_FIELD));

        return request;
    }
}
