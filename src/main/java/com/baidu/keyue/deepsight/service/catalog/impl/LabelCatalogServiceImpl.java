package com.baidu.keyue.deepsight.service.catalog.impl;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.models.catalog.CatalogDetail;
import com.baidu.keyue.deepsight.models.catalog.DeleteCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.EditCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.catalog.MoveCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelCatalogMapper;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@CacheConfig(cacheNames = "LabelCatalog")
public class LabelCatalogServiceImpl implements LabelCatalogService {

    @Autowired
    private RedissonClient redisson;
    @Autowired
    private ExtendLabelCatalogMapper labelCatalogMapper;

    private static final Integer MAX_CATALOG_LEVEL = 4;

    private String catalogLock(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Override
    public void createLabelCatalog(NewCatalogRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();

        String key = catalogLock(tenantId, request.getCatalogName(), request.getParentId().toString());
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            // 判断父级目录是否存在 && 父级目录层数最多为 (5 - 1) = 4
            if (request.notRootCatalog()) {
                parentCatalogLevelCheck(request.getParentId(), MAX_CATALOG_LEVEL);
            }

            // 根据用户 ID 、标签目录名、父级目录名称 查询同级重复记录
            if (isExistCatalogName(tenantId, request.getCatalogName(), request.getParentId())) {
                throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "标签目录已存在");
            }
            // insert into db
            LabelCatalog record = newLabelCatalogRecord(userId, tenantId, request.getParentId(), request.getCatalogName());
            labelCatalogMapper.insert(record);
        } catch (DeepSightException.CatalogOperatorFailedException e) {
            log.error("LabelCatalogService.newLabelCatalog error : ", e);
            throw new DeepSightException.CatalogOperatorFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("LabelCatalogService.newLabelCatalog INTERNAL_ERROR : ", e);
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.INTERNAL_ERROR, "标签目录创建失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void parentCatalogLevelCheck(Long parentId, int maxLevel) {
        long id = parentId;
        int times = 0;
        do {
            LabelCatalog catalog = labelCatalogMapper.selectByPrimaryKey(id);
            if (Objects.isNull(catalog)) {
                throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "上层标签目录不存在");
            }
            id = catalog.getParentId();
            times += 1;
        } while (id != 0);

        if (times > maxLevel) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "上层标签目录层级过多");
        }
    }

    private static LabelCatalog newLabelCatalogRecord(String userId, String tenantId, Long parentId, String catalogName) {
        LabelCatalog record = new LabelCatalog();
        record.setUserId(userId);
        record.setTenantId(tenantId);
        record.setParentId(parentId);
        record.setCatalogName(catalogName);
        record.setSort(DatetimeUtils.getCurrentTimestampInSec());
        record.setLabelCount(0);
        record.setDel(false);
        record.setCreator(userId);
        record.setModifier(userId);
        Date now = new Date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        return record;
    }

    @Override
    public void deleteLabelCatalog(DeleteCatalogRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        // 检查标签用户
        LabelCatalog labelCatalog = checkLabelCatalog(request.getCatalogId(), tenantId);

        if (!isEmptyCatalog(tenantId, request.getCatalogId())) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "标签目录下存在子目录，无法删除");
        }
        labelCatalog.setDel(DelEnum.DELETED.getBoolean());
        labelCatalog.setModifier(userId);
        labelCatalog.setUpdateTime(new Date());
        labelCatalogMapper.updateByPrimaryKey(labelCatalog);
    }

    @Override
    public void updateLabelCatalog(EditCatalogRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();

        LabelCatalog catalog = checkLabelCatalog(request.getCatalogId(), tenantId);
        if (catalog.getCatalogName().equals(request.getCatalogName()) && catalog.getId().equals(request.getCatalogId())) {
            return;
        }

        String key = catalogLock(tenantId, request.getCatalogName(), String.valueOf(catalog.getParentId()));
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            // 根据用户 ID 、标签目录名、父级目录名称 查询同级重复记录
            if (isExistCatalogName(tenantId, request.getCatalogName(), catalog.getParentId())) {
                throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "标签目录已存在");
            }
            // update catalog name
            catalog.setModifier(userId);
            catalog.setUpdateTime(new Date());
            catalog.setCatalogName(request.getCatalogName());
            labelCatalogMapper.updateByPrimaryKey(catalog);
        } catch (DeepSightException.CatalogOperatorFailedException e) {
            log.error("LabelCatalogService.newLabelCatalog error : ", e);
            throw new DeepSightException.CatalogOperatorFailedException(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("LabelCatalogService.newLabelCatalog INTERNAL_ERROR : ", e);
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.INTERNAL_ERROR, "标签目录修改失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void move(MoveCatalogRequest request) {
        Date now = new Date();
        // 移动当前目录到指定目录（after）之后，顶层目录为0
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        LabelCatalog catalog = checkLabelCatalog(request.getCatalogId(), tenantId);

        // 找到所有同级目录
        List<LabelCatalog> sameLevelCatalog = getSameLevelCatalogs(tenantId, catalog.getParentId());
        if (CollectionUtils.isEmpty(sameLevelCatalog)) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "同级目录为空");
        }
        // 同级目录置顶
        if (request.getAfter() == 0) {
            catalog.setModifier(userId);
            catalog.setUpdateTime(now);
            catalog.setSort(sameLevelCatalog.get(0).getSort() - 1);
            labelCatalogMapper.updateByPrimaryKey(catalog);
            return;
        }

        int afterIndex = -1;
        int nowIndex = -1;

        for (int i = 0; i < sameLevelCatalog.size(); i++) {
            if (sameLevelCatalog.get(i).getId().equals(request.getAfter())) {
                afterIndex = i;
            } else if (sameLevelCatalog.get(i).getId().equals(request.getCatalogId())) {
                nowIndex = i;
            }
        }

        // 目标缺失
        if (afterIndex < 0 || nowIndex < 0) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "目标标签目录不存在/非同级目录");
        }

        // 不需要移动
        if (afterIndex == nowIndex) {
            return;
        }

        // 更新当前标签目录的 sort 值
        long newSortValue = sameLevelCatalog.get(afterIndex).getSort() + 1;
        catalog.setModifier(userId);
        catalog.setUpdateTime(now);
        catalog.setSort(newSortValue);
        labelCatalogMapper.updateByPrimaryKey(catalog);

        List<Long> catalogIds;
        // 上移
        if (afterIndex < nowIndex) {
            // 中间的行 sort + 1
            catalogIds = sameLevelCatalog.subList(afterIndex + 1, nowIndex)
                    .stream()
                    .map(LabelCatalog::getId)
                    .filter(item -> !item.equals(request.getCatalogId()))
                    .toList();
        } else {
            // 下移
            // 中间的行 sort - 1
            catalogIds = sameLevelCatalog.subList(afterIndex + 1, sameLevelCatalog.size()).stream()
                    .map(LabelCatalog::getId)
                    .filter(item -> !item.equals(request.getCatalogId()))
                    .toList();
        }
        if (CollectionUtils.isNotEmpty(catalogIds)) {
            labelCatalogMapper.catalogMoveDown(catalogIds, now, userId);
        }
    }

    @Override
    public ListCatalogResponse list(ListCatalogRequest request) {
        String tenantId = request.getTenantId();
        if (StringUtils.isEmpty(tenantId)) {
            tenantId = String.valueOf(WebContextHolder.getTenantId());
        }

        // 获取所有标签目录
        LabelCatalogCriteria labelCatalogCriteria = new LabelCatalogCriteria();
        LabelCatalogCriteria.Criteria criteria = labelCatalogCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId);
        if (StringUtils.isNotBlank(request.getCatalogName())) {
            criteria.andCatalogNameLike("%" + request.getCatalogName() + "%");
        }
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        labelCatalogCriteria.setOrderByClause("sort asc");
        List<LabelCatalog> all = labelCatalogMapper.selectByExample(labelCatalogCriteria);

        List<CatalogDetail> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(all)) {
            return new ListCatalogResponse(results);
        }

        Map<Long, List<LabelCatalog>> catalogMap = all.stream().collect(Collectors.groupingBy(LabelCatalog::getParentId));
        long minParentId;
        if (Objects.nonNull(request.getCatalogId())) {
            minParentId = request.getCatalogId();
        } else {
            minParentId = catalogMap.keySet().stream().distinct().min(Long::compareTo).get();
        }

        // 根据 parentID(0) 自顶向下遍历
        results.addAll(buildCatalogList(catalogMap, minParentId));

        return new ListCatalogResponse(results);
    }

    @Override
    public List<CatalogDetail> buildCatalogList(Map<Long, List<LabelCatalog>> catalogMap, long parentId) {
        return catalogMap.getOrDefault(parentId, Lists.newArrayList()).stream()
                .sorted(Comparator.comparingLong(LabelCatalog::getSort))
                .map(catalog -> {
                    CatalogDetail detail = new CatalogDetail();
                    detail.setCatalogId(catalog.getId());
                    detail.setCatalogName(catalog.getCatalogName());
                    detail.setCount(0);

                    if (catalogMap.containsKey(catalog.getId())) {
                        detail.setChildren(buildCatalogList(catalogMap, catalog.getId()));
                    }
                    return detail;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> retrieveCatalogIds(Long labelCatalogId) {
        List<Long> catalogIds = Lists.newArrayList();
        if (Objects.isNull(labelCatalogId)) {
            return catalogIds;
        }
        ListCatalogRequest listCatalogRequest = new ListCatalogRequest();
        listCatalogRequest.setCatalogId(labelCatalogId);
        ListCatalogResponse all = list(listCatalogRequest);
        catalogIds.addAll(all.flattenCatalogIds());
        catalogIds.add(labelCatalogId);
        return catalogIds;
    }

    @Deprecated
    @Cacheable(cacheNames = "LabelCatalog", key = "#id")
    public String g(String id) {
        return "Cached-Value";
    }

    @Deprecated
    @CachePut(cacheNames = "LabelCatalog", key = "#id")
    public String u(String id) {
        return "Updated-Cache-Value";
    }

    @Deprecated
    @CacheEvict(cacheNames = "LabelCatalog", key = "#id")
    public String d(String id) {
        return null;
    }

    @Override
    public LabelCatalog getCatalogDetail(Long catalogId, String tenantId) {
        return checkLabelCatalog(catalogId, tenantId);
    }

    @Override
    public LabelCatalog checkLabelCatalog(String tenantId, String catalogName) {
        LabelCatalogCriteria labelCatalogCriteria = new LabelCatalogCriteria();
        labelCatalogCriteria.createCriteria()
                .andCatalogNameEqualTo(catalogName)
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<LabelCatalog> labelCatalogs = labelCatalogMapper.selectByExample(labelCatalogCriteria);
        if (CollectionUtils.isEmpty(labelCatalogs)) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.NOT_FOUND, "标签目录不存在");
        }
        return labelCatalogs.get(0);
    }

    @Override
    public void initTenantCatalog(String tenantId) {
        List<LabelCatalog> catalogs = Lists.newArrayList(
                newLabelCatalogRecord("", tenantId, 0L, "基础标签"),
                newLabelCatalogRecord("", tenantId, 0L, "偏好标签")
        );
        for (LabelCatalog record : catalogs) {
            try {
                if (!isExistCatalogName(record.getTenantId(), record.getCatalogName(), record.getParentId())) {
                    labelCatalogMapper.insert(record);
                }
            } catch (Exception e) {
                log.error("LabelCatalogService.initTenantCatalog error : ", e);
            }
        }
    }

    /**
     * 判断标签目录是否存在 true-存在；false-不存在
     * @param tenantId 租户id
     * @param catalogName 目录名称
     * @param parentId 父目录id
     */
    private boolean isExistCatalogName(String tenantId, String catalogName, Long parentId) {
        LabelCatalogCriteria labelCatalogCriteria = new LabelCatalogCriteria();
        labelCatalogCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andParentIdEqualTo(parentId)
                .andCatalogNameEqualTo(catalogName)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return labelCatalogMapper.countByExample(labelCatalogCriteria) > 0;
    }

    /**
     * 判断目录是否为空 true-空；false-非空
     * @param tenantId
     * @param parentId
     * @return
     */
    private boolean isEmptyCatalog(String tenantId, Long parentId) {
        LabelCatalogCriteria labelCatalogCriteria = new LabelCatalogCriteria();
        labelCatalogCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andParentIdEqualTo(parentId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return labelCatalogMapper.countByExample(labelCatalogCriteria) == 0;
    }

    /**
     * 校验未删除的标签目录
     * @param id 标签目录id
     */
    private LabelCatalog checkLabelCatalog(Long id, String tenantId) {
        LabelCatalogCriteria labelCatalogCriteria = new LabelCatalogCriteria();
        labelCatalogCriteria.createCriteria()
                .andIdEqualTo(id)
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<LabelCatalog> labelCatalogs = labelCatalogMapper.selectByExample(labelCatalogCriteria);

        if (CollectionUtils.isEmpty(labelCatalogs)) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.NOT_FOUND, "标签目录不存在");
        }
        return labelCatalogs.get(0);
    }

    /**
     * 获取同级目录列表
     * @param tenantId 租户id
     * @param parentId 父目录id
     */
    private List<LabelCatalog> getSameLevelCatalogs(String tenantId, Long parentId) {
        LabelCatalogCriteria labelCatalogCriteria = new LabelCatalogCriteria();
        labelCatalogCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andParentIdEqualTo(parentId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return labelCatalogMapper.selectByExample(labelCatalogCriteria);
    }
}
