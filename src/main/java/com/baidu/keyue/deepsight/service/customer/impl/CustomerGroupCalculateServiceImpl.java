package com.baidu.keyue.deepsight.service.customer.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.impl.BaseCalculateService;
import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BscTableIdentify;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskDataField;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskLabelCalculate;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskRequest;
import com.baidu.keyue.deepsight.models.customer.cal.CustomerGroupCalculateContext;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupCalculateService;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @className: CustomerGroupCalculateServiceImpl
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/16 16:57
 */
@Slf4j
@Service
public class CustomerGroupCalculateServiceImpl
        extends BaseCalculateService<CustomerGroupCalculateContext, CustomerGroup>
        implements CustomerGroupCalculateService {

    @Autowired
    private CustomerGroupService customerGroupService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private RuleParseService ruleParseService;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Override
    public void execByManual(GetCustomerGroupRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        CustomerGroup customerGroup = customerGroupService.getCustomerGroupByTenantIdAndCustomerGroupId(
                tenantId, request.getCustomerGroupId());
        // 手动更新检查是否为导入客群
        if (Objects.equals(GroupingTypeEnum.FILE_IMPORT.getCode(), customerGroup.getGroupingType())) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "导入人群不能再次更新");
        }
        // 手动执行客群计算
        execByManual(customerGroup, userId);
    }

    @Override
    @Transactional
    public void onFailure(CustomerGroupCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.FAILED);
        // 更新客群计算状态
        customerGroupService.updateCustomerGroupCalTaskStatus(calculateExecInstance.getCustomerGroup(), TaskExecStatusEnum.FAILED);
    }

    @Override
    @Transactional
    public void onFinishedHandler(CustomerGroupCalculateContext execContext) {

        // 数据从临时表迁移到宽表
        customerGroupValuesMigrate(execContext);

        if (Objects.nonNull(execContext.getErr())) {
            onFailure(execContext);
            return;
        }
        onSuccess(execContext);
    }

    @Override
    protected CustomerGroupCalculateContext getCalculateContextByJsonStr(String contextJsonStr) {
        return JsonUtils.toObjectWithoutException(contextJsonStr, CustomerGroupCalculateContext.class);
    }

    @Override
    public void invalidCustomerDorisFieldClear(CustomerGroup customerGroup) {
        String tenantId = customerGroup.getTenantId();
        String fieldStringName = String.format(Constants.DORIS_CUSTOMER_GROUP_FIELD_TEM, customerGroup.getId());
        dorisService.deleteLabelProcessField(
                dorisConfiguration.getDb(),
                TenantUtils.generateMockUserTableName(tenantId), fieldStringName);
    }

    /**
     * 客群计算成功，更新状态
     */
    @Transactional
    public void onSuccess(CustomerGroupCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.SUCCESS);
        // 更新标签计算状态
        customerGroupService.updateCustomerGroupCalTaskStatus(calculateExecInstance.getCustomerGroup(), TaskExecStatusEnum.SUCCESS);
    }

    /**
     * 合并客群bsc计算结果
     */
    private void customerGroupValuesMigrate(CustomerGroupCalculateContext execContext) {
        CustomerGroup customerGroup = execContext.getCustomerGroup();
        UpdateModEnum updateModEnum = UpdateModEnum.getByCode(customerGroup.getCustomerGroupValueUpdateMod());

        String from = String.format("%s.%s", execContext.getDb(), execContext.getTable());
        String to = String.format("%s.%s", execContext.getDb(), execContext.getFinalTable());
        String fieldName = execContext.getFieldName();
        String primaryKey = execContext.getPrimaryKey();

        if (UpdateModEnum.REPLACE.equals(updateModEnum)) {
            // old data clear
            String clearSql = String.format("update %s as t set t.%s = '0'", to, fieldName);
            try {
                dorisService.execSql(clearSql);
            } catch (Exception e) {
                execContext.setErr(new DeepSightException.DorisExecException("客群值数据清理执行失败"));
                log.error("CustomerGroupCalculateService.customerGroupValuesMigrate clear old data failed, sql: {}",
                        clearSql, e);
                return;
            }
        }

        String sql;
        String sqlTemplate = "UPDATE %s as t SET t.%s = s.%s FROM %s s WHERE t.%s = s.%s and s.%s != ''";
        sql = String.format(sqlTemplate, to, fieldName, fieldName, from, primaryKey, primaryKey, primaryKey);

        try {
            dorisService.execSql(sql);
        } catch (Exception e) {
            execContext.setErr(new DeepSightException.DorisExecException("客群值数据迁移执行失败"));
            log.error("CustomerGroupCalculateService.customerGroupValuesMigrate do data migrate failed, sql: {}",
                    sql, e);
            return;
        }

    }

    @Transactional
    @Override
    protected void onProcessing(CustomerGroupCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(calculateExecInstance, TaskExecStatusEnum.RUNNING);
        // 更新客群计算状态
        customerGroupService.updateCustomerGroupCalTaskStatus(calculateExecInstance.getCustomerGroup(), TaskExecStatusEnum.RUNNING);
    }

    @Override
    protected CustomerGroupCalculateContext initCalculateExecInstance(CustomerGroup customerGroup,
                                                                      String userId) {
        CustomerGroupCalculateContext calculateExecInstance = new CustomerGroupCalculateContext();
        calculateExecInstance.setCustomerGroup(customerGroup);

        // 创建执行记录
        calculateExecInstance.setExecId(taskSchedulerService.newTaskScheduler(customerGroup.getTask(), userId));

        // 设置主键，默认为 oneID
        calculateExecInstance.setPrimaryKey(Constants.TABLE_USER_ONE_ID);

        // 设置最终宽表
        calculateExecInstance.setDb(dorisConfiguration.getDb());
        calculateExecInstance.setFinalTable(TenantUtils.generateMockUserTableName(customerGroup.getTenantId()));

        // 设置计算临时表，临时表名使用 execId 计算
        calculateExecInstance.setTable(calculateExecInstance.generateTemporaryTableName());

        calculateExecInstance.setFieldName(calculateExecInstance.generateFieldName(customerGroup.getId()));

        // 初始化 bsc 任务结构体
        calculateExecInstance.setBscLabelTaskRequest(new BscLabelTaskRequest());
        calculateExecInstance.getBscLabelTaskRequest().setExecId(calculateExecInstance.getExecId());
        calculateExecInstance.getBscLabelTaskRequest().setUpdateModEnum(customerGroup.getCustomerGroupValueUpdateMod());
        calculateExecInstance.getBscLabelTaskRequest().setSaveModEnum(LabelValueSaveModEnum.SINGLE.getCode());

        return calculateExecInstance;
    }

    @Transactional
    @Override
    protected void onProcessingStatus(CustomerGroupCalculateContext calculateExecInstance) {
        // 更新调度记录
        taskSchedulerService.updateSchedulerStatus(calculateExecInstance.getExecId(), TaskExecStatusEnum.RUNNING);
        // 更新客群计算状态
        customerGroupService.updateCustomerGroupCalTaskStatus(calculateExecInstance.getCustomerGroup(), TaskExecStatusEnum.RUNNING);
    }

    @Override
    protected void taskCalculate(CustomerGroupCalculateContext calculateExecInstance) {
        CustomerGroup customerGroup = calculateExecInstance.getCustomerGroup();

        // 客群规则校验
        RuleGroup customerGroupRule = JsonUtils.toObjectWithoutException(customerGroup.getCustomerGroupRule(), RuleGroup.class);
        if (Objects.isNull(customerGroupRule)) {
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.BAD_REQUEST, "客群规则为空");
        }

        // Doris前置资源准备
        try {
            dorisService.createCustomerProcessTemporaryTable(
                    calculateExecInstance.getDb(),
                    calculateExecInstance.getTable(),
                    calculateExecInstance.getPrimaryKey(),
                    calculateExecInstance.getFieldName());
        } catch (Exception e) {
            calculateExecInstance.setErr(new DeepSightException.DorisExecException());
            return;
        }

        // 规则 SQL 解析
        buildCustomerSql(customerGroupRule, calculateExecInstance);
        if (Objects.nonNull(calculateExecInstance.getErr())) {
            return;
        }

        onFinishedHandler(calculateExecInstance);

        clearTmpTables(calculateExecInstance);
    }

    @Override
    protected String getBscJobCode(CustomerGroupCalculateContext execContext) {
        return null;
    }

    @Override
    protected String getBscResourceId() {
        return null;
    }

    @Override
    public List<Pair<CustomerGroup, TaskInfo>> pullWaitExecCustomerTask() {
        return this.pullWaitExecTask(TaskTypeEnum.CUSTOMER_DWS_TASK);
    }

    @Override
    protected List<CustomerGroup> getWaitExecTaskObjList(Set<Long> taskIdList) {
        return customerGroupService.getWaitExecCustomer(taskIdList);
    }

    private void buildCustomerSql(RuleGroup customerGroupRule,
                                  CustomerGroupCalculateContext calculateExecInstance) {
        List<BscLabelTaskLabelCalculate> calculates = Lists.newArrayList();
        DqlParseResult dqlParseResult;
        try {
            dqlParseResult = ruleParseService.parseRuleGroup(customerGroupRule, new AtomicInteger(0));
        } catch (Exception e) {
            throw new DeepSightException.CustomerGroupFailedException(ErrorCode.BAD_REQUEST, "客群规则解析失败");
        }
        calculates.add(new BscLabelTaskLabelCalculate("1", dqlParseResult.parseDorisSql(), 1));

        dqlParseResult.setSelect(dqlParseResult.getSelect() + " , '1' ");
        String sql = String.format("INSERT INTO `%s` ", calculateExecInstance.getTable()) + dqlParseResult.parseDorisSql();
        try {
            dorisService.execSql(sql);
        } catch (Exception e) {
            log.error("buildLabelValueSql exec sql error: {}, ", sql, e);
        }

        // 保存 sourceTable 元数据
        List<BscTableIdentify> sourceTables = Lists.newArrayList();

        // Source table schema
        calculateExecInstance.getBscLabelTaskRequest().setSourceTable(sourceTables);

        // 保存 sql 规则
        calculateExecInstance.getBscLabelTaskRequest().setCalculates(calculates);

        //  Sink table schema
        calculateExecInstance.getBscLabelTaskRequest().setSinkTable(Lists.newArrayList(
                new BscTableIdentify(
                        calculateExecInstance.getDb(),
                        calculateExecInstance.getTable(),
                        "",
                        Lists.newArrayList(
                                new BscLabelTaskDataField(calculateExecInstance.getPrimaryKey(), "varchar"),
                                new BscLabelTaskDataField(calculateExecInstance.getFieldName(), "varchar")
                        ))
        ));
    }

    private void clearTmpTables(CustomerGroupCalculateContext execContext) {
        // doris temporary table delete
        String temporaryTableName = execContext.getTable();
        if (StringUtils.isNotBlank(temporaryTableName)
                && StringUtils.contains(temporaryTableName, "temporary_t")) {
            dorisService.deleteLabelProcessTemporaryTable(execContext.getDb(), temporaryTableName);
        }
    }
}
