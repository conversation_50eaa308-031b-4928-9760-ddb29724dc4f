package com.baidu.keyue.deepsight.service.tenant.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tenant.TenantUpgradeHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @className TenantInfoServiceImpl
 * @description 租户信息
 * @date 2025/1/17 12:47
 */
@Service
@Slf4j
public class TenantInfoServiceImpl implements TenantInfoService {

    @Autowired
    private TenantInfoMapper tenantInfoMapper;

    @Autowired
    private List<TenantUpgradeHandler> upgradeHandlers;

    @Resource
    private RedissonClient redissonClient;


    @Override
    @Cacheable(value = "tenantInfo", cacheManager = "caffeineCacheManager",
            key = "#tenantId", unless = "#result == null")
    public TenantInfo queryTenantInfo(String tenantId) {
        TenantInfoCriteria tenantInfoCriteria = new TenantInfoCriteria();
        TenantInfoCriteria.Criteria criteria = tenantInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId);
        List<TenantInfo> tenantInfos = tenantInfoMapper.selectByExample(tenantInfoCriteria);
        return CollectionUtils.isNotEmpty(tenantInfos) ? tenantInfos.get(0) : null;
    }

    @Override
    public void saveTenantInfo(TenantInfo tenantInfo) {
        int i = tenantInfoMapper.insertSelective(tenantInfo);
    }

    @Override
    public void initOrUpgradeTenant(TenantDTO tenantDTO) {
        tenantDTO.setIsInit(ObjectUtils.isEmpty(tenantDTO.getTenantInfo()));
        // 加锁
        String lockKey = String.format("TENANT_IN_UPGRADE_%s", tenantDTO.getTenantId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = lock.tryLock();
        if (locked) {
            try {
                log.info("租户初始化或升级开始, lockKey:{}", lockKey);
                TenantUpgradeHandler tenantUpgradeHandler = upgradeHandlers.get(0);
                tenantUpgradeHandler.handle(tenantDTO);
                log.info("租户初始化或升级结束, lockKey:{}", lockKey);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            log.info("租户初始化或升级加锁失败跳过, lockKey:{}", lockKey);
        }
    }

    @Override
    public TenantInfo getDefaultTenant() {
        TenantInfoCriteria tenantInfoCriteria = new TenantInfoCriteria();
        TenantInfoCriteria.Criteria criteria = tenantInfoCriteria.createCriteria();
        criteria.andTenantSourceEqualTo(Constants.TENANT_DEFAULT_TYPE).andTenantidEqualTo(Constants.TENANT_DEFAULT_ID);
        List<TenantInfo> tenantInfos = tenantInfoMapper.selectByExample(tenantInfoCriteria);
        return CollectionUtils.isNotEmpty(tenantInfos) ? tenantInfos.get(0) : null;
    }

    @Override
    public List<TenantInfo> getUpgradeTenant(Integer version) {
        TenantInfoCriteria tenantInfoCriteria = new TenantInfoCriteria();
        TenantInfoCriteria.Criteria criteria = tenantInfoCriteria.createCriteria();
        criteria.andVersionLessThan(version);
        return tenantInfoMapper.selectByExample(tenantInfoCriteria);
    }

    @Override
    public List<TenantInfo> getAllTenantInfo() {
        return tenantInfoMapper.selectByExample(new TenantInfoCriteria());
    }
}
