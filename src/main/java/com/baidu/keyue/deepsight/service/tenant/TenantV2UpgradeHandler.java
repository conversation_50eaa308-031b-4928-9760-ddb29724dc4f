package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className TenantV2UpgradeHandler
 * @description
 * @date 2025年04月15日15:37:59
 */
@Slf4j
@Service
@Order(3)
public class TenantV2UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;
    
    @Resource
    private TenantV3UpgradeHandler tenantV3UpgradeHandler;
    
    public TenantV2UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @PostConstruct
    public void init() throws IOException {
        setVersion(3);
        initDorisSql("upgrade/doris/v1.1.1.sql");
        addNextHandler(tenantV3UpgradeHandler);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        Integer version = tenantInfoService.queryTenantInfo(tenant.getTenantid()).getVersion();
        return Objects.equals(version, 2);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                String tenantId = tenant.getTenantId();
                log.info("tenant v1.1.1 upgrade start.tenantId is {}.", tenantId);
                // 初始化租户新版本Doris表
                execDorisTableSql(tenantId);
                log.info("tenant v1.1.1 execDorisTableSql finished init.tenantId is {}.", tenantId);
                // 更新责任链租户信息
                TenantInfo info = tenant.getTenantInfo();
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("tenant v1.1.1  Upgrade error!, tenantId:{} ", tenant.getTenantId(), e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1.1.1版本升级失败");
            }
            return null;
        });
    }
}
