package com.baidu.keyue.deepsight.service.diffusion;

import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 人群扩散 Service Interface
 */
public interface GroupDiffusionCalculateService {

    /**
     * 执行人群扩散预测任务
     *
     * @param diffusionId 人群扩散预测主键 ID
     */
    void execGroupDiffusion(long diffusionId, String userId);

    /**
     * 获取人群扩散预测结果
     *
     * @param execId 执行 ID
     */
    void fetchGroupDiffusionResult(long execId);

    /**
     * 拉取待执行的任务
     *
     * @param statusEnum
     * @return 任务集合
     */
    List<Pair<CustomerDiffusionTask, TaskInfo>> pullTaskByStatus(TaskExecStatusEnum statusEnum);

    /**
     * 将人群扩散结果打包到客群
     *
     * @param customerDiffusionTaskId 人群扩散任务 id
     */
    void packageAsCustomerGroup(long customerDiffusionTaskId);


    /**
     * 拉取正在执行中的任务
     *
     * @param
     * @return 任务集合
     */
    List<TaskSchedulerWithBLOBs> pullRunningTask();

    /**
     * 拉取正在执行中的任务
     *
     * @param
     * @return 任务集合
     */
    List<CustomerDiffusionTask> pullRunningDiffusionTask();
}
