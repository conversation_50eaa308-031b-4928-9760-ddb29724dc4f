package com.baidu.keyue.deepsight.service.diffusion.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.FeatureSelectEnum;
import com.baidu.keyue.deepsight.enums.JudgeCriteriaEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskCreateRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskListRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskRetryRequest;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskCreateRes;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskRes;
import com.baidu.keyue.deepsight.models.customer.response.GroupDetailDto;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.StringUtil;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class GroupDiffusionServiceImpl implements GroupDiffusionService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private CustomerDiffusionTaskMapper customerDiffusionTaskMapper;

    @Resource
    private CustomerGroupService customerGroupService;

    @Resource
    private TaskInfoService taskInfoService;
    
    @Resource
    private DorisService dorisService;

    @Autowired
    private OperationService operationService;

    @Value("${customer-group.seed-proportion:0.5}")
    private Double seedProportion;
    
    @Value("${customer-group.system-recommend:30}")
    private Float systemRecommend;

    @Override
    @Transactional
    public CustomerDiffusionTaskCreateRes createTask(CustomerDiffusionTaskCreateRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String userId = WebContextHolder.getUserId();
        String userName = WebContextHolder.getUserName();
        String taskName = request.getTaskName();
        String lockKey = StrUtil.join("_", "CustomerDiffusionTask", tenantId, taskName);
        RLock lock = redissonClient.getLock(lockKey);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        CustomerDiffusionTaskCreateRes res = new CustomerDiffusionTaskCreateRes();
        try {
            // 运营模式
            OperationModeEnum operationModeResponse = operationService.detectTenantOperationMode(tenantId);

            // 客群校验
            Set<Long> predictGroup = request.getPredictGroup();
            Long seedGroup = request.getSeedGroup();
            if (predictGroup.contains(seedGroup)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BUSY_REQUEST, "预测人群不能包含种子人群");
            }
            List<Long> queryIds = new ArrayList<>(predictGroup);
            queryIds.add(seedGroup);
            List<CustomerGroup> customerGroups = customerGroupService.retrieveCustomerGroupWithIds(queryIds, tenantId);
            // 结果为空，或者结果少于原IDS数量，则存在无效客群
            if (CollUtil.isEmpty(customerGroups) || !Objects.equals(customerGroups.size(), queryIds.size())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BUSY_REQUEST, "种子人群和预测人群必须属于当前租户");
            }
            // 任务名称重复校验
            CustomerDiffusionTaskCriteria taskCriteria = new CustomerDiffusionTaskCriteria();
            taskCriteria.createCriteria()
                    .andDelEqualTo(false)
                    .andTenantIdEqualTo(tenantId)
                    .andTaskNameEqualTo(taskName);
            long count = customerDiffusionTaskMapper.countByExample(taskCriteria);
            Assert.isTrue(count == 0, () -> new DeepSightException.ParamsErrorException(ErrorCode.BUSY_REQUEST, "任务名重复"));
            // 校验：种子人群数需要小于搜索范围人群数之和，具体比例xxx
            Long seedCount = customerGroupService.getUserCount(tenantId, seedGroup);
            Assert.isFalse(Objects.equals(seedCount, 0L),
                    () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "种子人群数不能为0"));
            Long predictCount = customerGroupService.getUserCount(tenantId, request.getPredictGroup());
            Assert.isFalse(Objects.equals(predictCount, 0L),
                    () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "预测总人数不能为0"));
            Assert.isFalse((double) seedCount / predictCount >= seedProportion,
                    () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, 
                            "种子人群人数占全部人群人数比例需要小于" + NumberUtil.formatPercent(seedProportion, 0)));
            // 特征筛选校验
            FeatureSelectEnum featureSelect = request.getFeatureSelect();
            Float threshold = request.getThreshold();
            if (Objects.equals(FeatureSelectEnum.COVER_CUSTOMIZE, featureSelect)) {
                // 传参校验
                Assert.isTrue(threshold != null && threshold > 0, 
                        () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "覆盖率不能为空且必须大于0"));
            } else {
                request.setThreshold(systemRecommend);
                threshold = systemRecommend;
            }
            // 覆盖人数校验：种子人群&预测人群覆盖人数不能为0
            String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
            List<TableDescribeDto> tableSchemas = dorisService.describeTableSchema(mockUserTableName);
            Pair<String, String> seedSqlPair = ORMUtils.generateFetchPredictGroupDataSql(mockUserTableName, tableSchemas,
                    List.of(seedGroup), threshold, operationModeResponse);
            Pair<String, String> predictSqlPair = ORMUtils.generateFetchPredictGroupDataSql(mockUserTableName, tableSchemas,
                    List.copyOf(request.getPredictGroup()), threshold, operationModeResponse);
            long seedCoverCount = dorisService.getCount(seedSqlPair.getLeft());
            Assert.isTrue(seedCoverCount > 0,
                    () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "种子人群特征覆盖率不满足要求"));
            long predictCoverCount = dorisService.getCount(predictSqlPair.getLeft());
            Assert.isTrue(predictCoverCount > 0,
                    () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "预测人群特征覆盖率不满足要求"));
            // 判定标准校验
            JudgeCriteriaEnum judgeCriteria = request.getJudgeCriteria();
            Float similarity = request.getSimilarity();
            Integer ranking = request.getRanking();
            if (Objects.equals(JudgeCriteriaEnum.SIMILARITY, judgeCriteria) && (similarity == null || similarity <= 0)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "相似度不能为空且必须大于0");
            } else if (Objects.equals(JudgeCriteriaEnum.RANKING, judgeCriteria) && (ranking == null || ranking <= 0)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "判定取值不能为空且必须大于0");
            }
            // 更新触发类型校验
            TriggerModeEnum triggerMod = request.getTriggerMod();
            TriggerFrequencyEnum triggerFrequency = request.getTriggerFrequency();
            TriggerFrequencyValue frequencyValue = request.getTriggerFrequencyValue();
            if (Objects.equals(TriggerModeEnum.CRON, triggerMod) && (triggerFrequency == null || frequencyValue == null)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "定时更新执行屁频率不能为空");
            }
            // 创建任务
            Long taskId = taskInfoService.createTask(
                    TaskTypeEnum.CUSTOMER_DIFFUSION,
                    request.getTriggerMod(),
                    request.getTriggerFrequency(),
                    request.getTriggerFrequencyValue());
            // 创建预测任务
            CustomerDiffusionTask task = new CustomerDiffusionTask();
            BeanUtils.copyProperties(request, task);
            task.setTaskId(taskId);
            task.setPredictGroup(StrUtil.join(",", request.getPredictGroup()));
            task.setFeatureSelect(request.getFeatureSelect().getCode());
            task.setFilterRule(request.getFilterRule().getCode());
            task.setJudgeCriteria(request.getJudgeCriteria().getCode());
            task.setTriggerMod(request.getTriggerMod().getCode());
            task.setTenantId(tenantId);
            task.setCreator(userId);
            task.setModifier(userId);
            task.setCreatorName(userName);
            task.setModifierName(userName);
            // 仅定时更新任务，初始状态为待执行
            if (Objects.equals(TriggerModeEnum.REALTIME, triggerMod)) {
                task.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
            } else {
                task.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
            }
            if (triggerFrequency != null) {
                task.setTriggerFrequency(triggerFrequency.getCode());
            }
            if (frequencyValue != null) {
                task.setTriggerFrequencyValue(JSONUtil.toJsonStr(frequencyValue));
            }
            customerDiffusionTaskMapper.insertSelective(task);
            res.setId(task.getId());
            return res;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<CustomerDiffusionTask> getCustomerDiffusionTaskWithId(long diffusionId) {
        CustomerDiffusionTaskCriteria customerDiffusionTaskCriteria = new CustomerDiffusionTaskCriteria();
        customerDiffusionTaskCriteria.createCriteria()
                .andIdEqualTo(diffusionId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return customerDiffusionTaskMapper.selectByExample(customerDiffusionTaskCriteria);
    }

    @Override
    public BasePageResponse.Page<CustomerDiffusionTaskRes> getTaskPageList(CustomerDiffusionTaskListRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        CustomerDiffusionTaskCriteria taskCriteria = new CustomerDiffusionTaskCriteria();
        CustomerDiffusionTaskCriteria.Criteria criteria = taskCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(false);
        String taskName = request.getTaskName();
        if (StrUtil.isNotBlank(taskName)) {
            criteria.andTaskNameLike(StringUtil.getSqlLike(taskName));
        }
        long count = customerDiffusionTaskMapper.countByExample(taskCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        PageHelper.startPage(request.getPageNo(), request.getPageSize());
        taskCriteria.setOrderByClause(Constants.ORDER_BY_CREATE_TIME_DESC);
        List<CustomerDiffusionTask> customerDiffusionTasks = customerDiffusionTaskMapper.selectByExample(taskCriteria);
        List<CustomerDiffusionTaskRes> resList = new ArrayList<>();
        customerDiffusionTasks.forEach(task -> resList.add(new CustomerDiffusionTaskRes(task)));
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, resList);
    }

    @Override
    public void deleteTask(Long id, String tenantId, String userId, String userName) {
        CustomerDiffusionTaskRes taskDetail = getTaskDetail(id, tenantId, false);
        Assert.notNull(taskDetail, () -> new DeepSightException.ParamsErrorException(ErrorCode.BUSY_REQUEST, "任务不存在"));
        // TODO 是否存在离线任务判断
        CustomerDiffusionTask updateTask = new CustomerDiffusionTask();
        updateTask.setId(id);
        updateTask.setModifier(userId);
        updateTask.setDel(true);
        updateTask.setModifierName(userName);
        customerDiffusionTaskMapper.updateByPrimaryKeySelective(updateTask);
    }

    @Override
    public CustomerDiffusionTaskRes getTaskDetail(Long id, String tenantId, boolean needCount) {
        CustomerDiffusionTaskCriteria taskCriteria = new CustomerDiffusionTaskCriteria();
        taskCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andIdEqualTo(id)
                .andDelEqualTo(false);
        List<CustomerDiffusionTask> diffusionTasks = customerDiffusionTaskMapper.selectByExample(taskCriteria);
        if (CollUtil.isEmpty(diffusionTasks)) {
            return null;
        }
        CustomerDiffusionTask task = diffusionTasks.get(0);
        CustomerDiffusionTaskRes taskRes = new CustomerDiffusionTaskRes(task);
        // 统计种子、预测人群数量
        if (needCount) {
            List<Long> ids = taskRes.getPredictGroup();
            List<GroupDetailDto> predictGroupDetail = new ArrayList<>();
            Long seedGroupId = taskRes.getSeedGroup();
            ids.add(seedGroupId);
            List<CustomerGroup> customerGroups = customerGroupService.retrieveCustomerGroupWithIds(ids, tenantId);
            // 统计人数
            for (CustomerGroup group : customerGroups) {
                Long groupId = group.getId();
                String customerGroupName = group.getCustomerGroupName();
                if (Objects.equals(groupId, seedGroupId)) {
                    Long seedCount = customerGroupService.getUserCount(tenantId, seedGroupId);
                    GroupDetailDto seedVo = new GroupDetailDto(groupId, customerGroupName, seedCount);
                    taskRes.setSeedGroupDetail(seedVo);
                } else {
                    Long count = customerGroupService.getUserCount(tenantId, groupId);
                    GroupDetailDto vo = new GroupDetailDto(groupId, customerGroupName, count);
                    predictGroupDetail.add(vo);
                }
            }
            taskRes.setPredictGroupDetail(predictGroupDetail);
        }
        // 存在打包客群ID，判断客群是否存在，打包客群已删除则可支持再次打包
        Long customerGroupId = task.getCustomerGroupId();
        if (customerGroupId != null) {
            CustomerGroup customerGroup = customerGroupService.getByIdAndTenantId(customerGroupId, tenantId);
            taskRes.setGroupPackage(customerGroup != null);
        }
        return taskRes;
    }

    @Override
    public void retryTask(CustomerDiffusionTaskRetryRequest request, String userId, String tenantId, String userName) {
        // 查询预测任务 手动更新且预测失败的方能重新预测
        Long id = request.getId();
        RLock lock = redissonClient.getLock("GROUP_DIFFUSION_RETRY_" + id);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            CustomerDiffusionTaskCriteria taskCriteria = new CustomerDiffusionTaskCriteria();
            taskCriteria.createCriteria()
                    .andDelEqualTo(false)
                    .andIdEqualTo(id)
                    .andTriggerModEqualTo(TriggerModeEnum.MANUAL.getCode())
                    .andCalStatusEqualTo(TaskExecStatusEnum.FAILED.getCode())
                    .andTenantIdEqualTo(tenantId);
            List<CustomerDiffusionTask> customerDiffusionTasks = customerDiffusionTaskMapper.selectByExample(taskCriteria);
            if (CollUtil.isEmpty(customerDiffusionTasks)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务不存在");
            }
            // 更新任务状态值为：待计算
            CustomerDiffusionTask diffusionTask = customerDiffusionTasks.get(0);
            CustomerDiffusionTask task = new CustomerDiffusionTask();
            task.setId(diffusionTask.getId());
            task.setModifier(userId);
            task.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
            task.setModifierName(userName);
            customerDiffusionTaskMapper.updateByPrimaryKeySelective(task);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional
    public void execByManual(CustomerDiffusionTaskRetryRequest request, String userId, String tenantId, String userName) {
        Long id = request.getId();
        RLock lock = redissonClient.getLock("GROUP_DIFFUSION_MANUAL_" + id);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            CustomerDiffusionTaskRes taskRes = getByIdAndTenantId(id, tenantId);
            Assert.notNull(taskRes, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务不存在"));
            if (Objects.equals(TaskExecStatusEnum.RUNNING, taskRes.getCalStatus())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务计算中无法执行");
            }
            CustomerDiffusionTask task = new CustomerDiffusionTask();
            task.setId(taskRes.getId());
            task.setModifier(userId);
            task.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
            task.setModifierName(userName);
            customerDiffusionTaskMapper.updateByPrimaryKeySelective(task);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public CustomerDiffusionTaskRes getByIdAndTenantId(Long id, String tenantId) {
        CustomerDiffusionTaskCriteria taskCriteria = new CustomerDiffusionTaskCriteria();
        taskCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andIdEqualTo(id)
                .andDelEqualTo(false);
        List<CustomerDiffusionTask> diffusionTasks = customerDiffusionTaskMapper.selectByExample(taskCriteria);
        if (CollUtil.isEmpty(diffusionTasks)) {
            return null;
        }
        CustomerDiffusionTask task = diffusionTasks.get(0);
        return new CustomerDiffusionTaskRes(task);
    }
}
