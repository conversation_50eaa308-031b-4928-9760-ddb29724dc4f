package com.baidu.keyue.deepsight.service.memory;

import cn.hutool.core.date.DateTime;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.MemoryTypeEnum;
import com.baidu.keyue.deepsight.enums.PromptTypeEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.YesEnum;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.memory.DeleteUserMemoryDataRequest;
import com.baidu.keyue.deepsight.models.memory.MemoryDataSourceDetail;
import com.baidu.keyue.deepsight.models.memory.MemoryDataSourceIDRequest;
import com.baidu.keyue.deepsight.models.memory.MemoryDataSourceSwitchUpdateRequest;
import com.baidu.keyue.deepsight.models.memory.NewMemoryDataSourceRequest;
import com.baidu.keyue.deepsight.models.memory.UpdateMemoryDataSourceRequest;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDataByDate;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetail;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetailRequest;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetailResponse;
import com.baidu.keyue.deepsight.models.memory.UserMemoryRowByDay;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtract;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendMemoryExtractMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.user.UserService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MemoryService {
    @Autowired
    private RedissonClient redisson;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Autowired
    private ExtendMemoryExtractMapper memoryExtractMapper;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private RuleParseService ruleParseService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private UserService userService;

    private final Base64.Encoder encoder = Base64.getEncoder();

    private String memoryRecordNameLock(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Transactional
    public void createMemoryDataSource(NewMemoryDataSourceRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();

        // 规则检查
        checkMemoryDataSource(request);

        // 数据集检查
        DataTableInfo tableInfo = getDataSetTableDetail(request.getDatasetId());
        if (Objects.isNull(tableInfo) || !tenantId.equals(tableInfo.getTenantid())) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "抽取数据集不存在");
        }
        TableFieldMetaInfo fieldMetaInfo = getFiledMetaInfoMap(request.getFieldId(), request.getDatasetId());
        if (Objects.isNull(fieldMetaInfo)) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "抽取字段不存在");
        }

        String key = memoryRecordNameLock(tenantId, request.getDatasetId().toString(), request.getFieldId().toString());
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            // 检查数据集抽取是否重复
            duplicateCheck(tenantId, request.getDatasetId(), request.getFieldId());

            MemoryExtractWithBLOBs record = new MemoryExtractWithBLOBs();
            record.setTenantId(tenantId);
            record.setDataTableId(request.getDatasetId());
            record.setDatasetName(request.getDatasetName());
            record.setFieldId(request.getFieldId());
            record.setFieldName(request.getFieldName());
            record.setFieldNameEn(fieldMetaInfo.getEnField());
            record.setPrompt(Objects.isNull(request.getPrompt()) ? "" : request.getPrompt());
            record.setPromptType(request.getPromptType().getCode());
            record.setTriggerMod(request.getTriggerMode().getCode());
            if (Objects.nonNull(request.getTriggerFrequency())) {
                record.setTriggerFrequency(request.getTriggerFrequency().getCode());
            }
            if (Objects.nonNull(request.getTriggerFrequencyValue())) {
                record.setTriggerFrequencyValue(JsonUtils.toJsonWithOutException(request.getTriggerFrequencyValue()));
            }
            record.setDescription(request.getDescription());
            record.setDataFilterRule(JsonUtils.toJsonWithOutException(request.getDataFilterRule()));
            record.setStatus(SwitchEnum.ON.getBoolean());
            record.setIsDefault(request.getIsDefault().getBoolean());
            record.setCalStatus(TaskExecStatusEnum.PENDING.getCode());

            Long taskId = taskInfoService.createMemoryExtractTask(
                    request.getTriggerMode(),
                    request.getTriggerFrequency(),
                    request.getTriggerFrequencyValue(),
                    userId);
            record.setTask(taskId);

            record.setDel(DelEnum.NOT_DELETED.getBoolean());
            record.setCreator(userId);
            record.setModifier(userId);
            Date now = new Date();
            record.setCreateTime(now);
            record.setUpdateTime(now);
            memoryExtractMapper.insert(record);
        } catch (DeepSightException.MemoryExtractOperatorFailedException e) {
            log.error("createMemoryDataSource error", e);
            throw e;
        } catch (Exception e) {
            log.error("createMemoryDataSource error", e);
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "创建记忆抽取配置失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void checkMemoryDataSource(NewMemoryDataSourceRequest request) {
        if (PromptTypeEnum.CUSTOM.equals(request.getPromptType()) && StringUtils.isBlank(request.getPrompt())) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "请输入抽取指令");
        }

        if (CollectionUtils.isEmpty(request.getDataFilterRule())) {
            return;
        }
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.DATASET);
        ruleNode.setDataTableId(request.getDatasetId());
        ruleNode.setFilters(request.getDataFilterRule());
        checkMemoryDatasourceFilterRule(ruleNode);
    }

    private void checkMemoryDatasourceFilterRule(RuleNode ruleNode) {
        try {
            ruleParseService.checkRuleNode(RuleNode.clone(ruleNode));
        } catch (Exception e) {
            log.error("checkMemoryDatasourceFilterRule error", e);
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "数据筛选规则解析失败");
        }
    }

    private MemoryExtractWithBLOBs selectMemoryDataSourceWithBLOBsById(Long id) {
        String tenantId = WebContextHolder.getTenantId();
        MemoryExtractCriteria criteria = new MemoryExtractCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andIdEqualTo(id);
        List<MemoryExtractWithBLOBs> record = memoryExtractMapper.selectByExampleWithBLOBs(criteria);
        if (CollectionUtils.isEmpty(record)) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.NOT_FOUND, "记忆抽取配置不存在");
        }
        return record.get(0);
    }

    private MemoryExtract selectMemoryDataSourceById(Long id) {
        String tenantId = WebContextHolder.getTenantId();
        MemoryExtractCriteria criteria = new MemoryExtractCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andIdEqualTo(id);
        List<MemoryExtract> record = memoryExtractMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(record)) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.NOT_FOUND, "记忆抽取配置不存在");
        }
        return record.get(0);
    }

    private void duplicateCheck(String tenantId, Long datasetId, Long fieldId) {
        MemoryExtractCriteria criteria = new MemoryExtractCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andDataTableIdEqualTo(datasetId)
                .andFieldIdEqualTo(fieldId);
        List<MemoryExtract> record = memoryExtractMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(record)) {
            return;
        }
        throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.NOT_FOUND, "抽取范围重复");
    }

    @Transactional
    public void updateMemoryConfig(UpdateMemoryDataSourceRequest request) {
        String userId = WebContextHolder.getUserId();

        MemoryExtractWithBLOBs record = selectMemoryDataSourceWithBLOBsById(request.getId());
        record.setTriggerMod(request.getTriggerMode().getCode());
        record.setTriggerFrequency(request.getTriggerFrequency().getCode());
        if (Objects.nonNull(request.getTriggerFrequencyValue())) {
            record.setTriggerFrequencyValue(JsonUtils.toJsonWithOutException(request.getTriggerFrequencyValue()));
        }
        record.setDescription(request.getDescription());

        record.setModifier(userId);
        record.setUpdateTime(new Date());
        record.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
        memoryExtractMapper.updateByPrimaryKeyWithBLOBs(record);

        taskInfoService.updateMemoryExtractTaskTrigger(
                record.getTask(),
                request.getTriggerMode(), request.getTriggerFrequency(), request.getTriggerFrequencyValue());
    }

    @Transactional
    public void deleteMemoryDataSource(MemoryDataSourceIDRequest request) {
        String userId = WebContextHolder.getUserId();
        MemoryExtractWithBLOBs record = selectMemoryDataSourceWithBLOBsById(request.getId());

        if (record.getIsDefault()) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "默认记忆抽取配置不允许删除");
        }

        record.setModifier(userId);
        record.setUpdateTime(new Date());
        record.setDel(DelEnum.DELETED.getBoolean());

        memoryExtractMapper.updateByPrimaryKeyWithBLOBs(record);
        taskInfoService.deleteFieldById(record.getTask());
    }

    @Transactional
    public void updateSwitch(MemoryDataSourceSwitchUpdateRequest request) {
        MemoryExtract record = selectMemoryDataSourceById(request.getId());
        record.setStatus(request.getStatus().getBoolean());
        if (SwitchEnum.OFF.equals(request.getStatus())) {
            record.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
        }
        memoryExtractMapper.updateByPrimaryKey(record);
    }

    public BasePageResponse.Page<MemoryDataSourceDetail> listDataSource(BasePageRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        Integer limit = request.getPageSize();

        MemoryExtractCriteria queryCriteria = new MemoryExtractCriteria();
        queryCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        long count = memoryExtractMapper.countByExample(queryCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }
        if (offset > count) {
            throw new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.BAD_REQUEST, "分页大小超过限制");
        }
        queryCriteria.setOrderByClause(String.format("id desc limit %d offset %d", limit, offset));
        List<MemoryExtractWithBLOBs> recordPage = memoryExtractMapper.selectByExampleWithBLOBs(queryCriteria);

        List<MemoryDataSourceDetail> recordBriefs = recordPage.stream()
                .map(MemoryDataSourceDetail::from)
                .toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, recordBriefs);
    }

    public void deleteMemoryResult(DeleteUserMemoryDataRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String memoryExtractResultTableName = TenantUtils.generateMemoryExtractTableName(tenantId);
        if (StringUtils.isBlank(request.getUserId()) && StringUtils.isBlank(request.getOneId())) {
            return;
        }
        String oneId = request.getOneId();
        if (StringUtils.isBlank(oneId)) {
            oneId = userService.getUserOneIdByUserId(tenantId, request.getUserId());
        }
        if (StringUtils.isBlank(oneId)) {
            log.warn("deleteMemoryResult failed, userId not exists: " + request.getUserId() + ", tenantId:" + tenantId);
            return;
        }

        try {
            String sql = ORMUtils.clearMemoryExtractResult(memoryExtractResultTableName, oneId);
            dorisService.execSql(sql);
        } catch (Exception e) {
            log.error("deleteMemoryResult error", e);
        }
    }

    public DataTableInfo getDataSetTableDetail(long tableId) {
        return dataTableInfoMapper.selectByPrimaryKey(tableId);
    }

    public TableFieldMetaInfo getFiledMetaInfoMap(long fieldId, long tableId) {
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andIdEqualTo(fieldId).andDataTableIdEqualTo(tableId);
        List<TableFieldMetaInfo> fieldInfos = tableFieldMetaInfoMapper.selectByExample(fieldMetaInfoCriteria);
        if (CollectionUtils.isEmpty(fieldInfos)) {
            return null;
        }
        return fieldInfos.get(0);
    }

    public UserMemoryDetailResponse queryUserMemoryDetail(UserMemoryDetailRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String memoryExtractResultTableName = TenantUtils.generateMemoryExtractTableName(tenantId);

        // build sql
        String sql = "SELECT * FROM " + memoryExtractResultTableName;

        if (StringUtils.isNotBlank(request.getQueryText())
                || request.getType() != null
                || StringUtils.isNotBlank(request.getUserId())) {

            List<String> whereConditions = Lists.newArrayList();

            if (StringUtils.isNotBlank(request.getOneId())) {
                whereConditions.add(" oneId = '" + StringEscapeUtils.escapeSql(request.getOneId()) + "' ");
            } else if (StringUtils.isNotBlank(request.getUserId())) {
                String oneId = userService.getUserOneIdByUserId(tenantId, request.getUserId());
                if (StringUtils.isNotBlank(oneId)) {
                    whereConditions.add(" oneId = '" + StringEscapeUtils.escapeSql(oneId) + "' ");
                }
            }

            if (StringUtils.isNotBlank(request.getQueryText())) {
                whereConditions.add(" memory_content like '%" + StringEscapeUtils.escapeSql(request.getQueryText()) + "%' ");
            }
            if (request.getType() != null) {
                String memoryType = request.getType().getCode();
                whereConditions.add(" memory_type = '" + StringEscapeUtils.escapeSql(memoryType) + "' ");
            }

            if (CollectionUtils.isNotEmpty(whereConditions)) {
                sql += " WHERE ";
                sql += StringUtils.join(whereConditions, " AND ");
            }
        }
        sql += " ORDER BY extract_date DESC";

        List<Map<String, Object>> recordList = Lists.newArrayList();
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("MemoryService.queryUserMemoryDetail selectList error", e);
        }

        Map<String, List<UserMemoryRowByDay>> memoryResultsByDay = recordList.stream().map(map -> {
                    DateTime extractDate = DatetimeUtils.fromDatetimeStr(map.get("extract_date").toString());
                    String memoryContent = map.get("memory_content").toString();
                    if (StringUtils.isBlank(memoryContent) || "null".equals(memoryContent)) {
                        return null;
                    }
                    if (extractDate == null) {
                        log.warn("queryUserMemoryDetail extractDate parser is null,{}", map.get("extract_date").toString());
                        return null;
                    }

                    UserMemoryRowByDay row = new UserMemoryRowByDay();
                    row.setId(Long.valueOf(map.get("id").toString()));
                    row.setUserId(map.get("user_id").toString());
                    row.setExtractDate(extractDate);
                    row.setMemoryContent(memoryContent);
                    row.setMemoryType(MemoryTypeEnum.getDescByCode(map.get("memory_type").toString()));
                    row.setDeepsightDatetime(DatetimeUtils.fromDatetimeStr(map.get("deepsight_datetime").toString(), DatetimeUtils.DATE_TIME_ZONE_FORMATTER));

                    row.dateComplete(extractDate);
                    return row;
                }).filter(Objects::nonNull).collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                UserMemoryRowByDay::getMemoryContent,
                                Function.identity(),
                                (existing, replacement) -> existing),
                        Map::values))
                .stream()
                .sorted(Comparator.comparing(UserMemoryRowByDay::getExtractDate).reversed())
                .collect(Collectors.groupingBy(UserMemoryRowByDay::getDayWithWeek));

        List<UserMemoryDataByDate> userMemoryDataByDates = Lists.newArrayList();
        for (Map.Entry<String, List<UserMemoryRowByDay>> entry : memoryResultsByDay.entrySet()) {
            UserMemoryDataByDate d = new UserMemoryDataByDate();
            d.setDay(entry.getKey());
            d.setMemoryList(entry.getValue().stream().map(item -> {
                UserMemoryDetail detail = new UserMemoryDetail();
                detail.setDatetime(item.getHour());
                detail.setContent(item.getMemoryContent());
                detail.setType(item.getMemoryType());
                return detail;
            }).collect(Collectors.toList()));

            userMemoryDataByDates.add(d);
        }

        return new UserMemoryDetailResponse(
                userMemoryDataByDates.stream().sorted(Comparator.comparing(UserMemoryDataByDate::getDay).reversed())
                        .collect(Collectors.toList())
        );
    }

    public void initTenantMemoryExtractTask(String tenantId, String userId) {
        String key = memoryRecordNameLock(tenantId, "initTenantMemoryExtractTask");
        RLock lock = redisson.getLock(key);
        if (!lock.tryLock()) {
            return;
        }

        try {
            // 初始化外呼记录表提取任务
            String sessionTable = TenantUtils.generateAiobSessionTableName(tenantId);
            initTenantMemoryTableTask(tenantId, Constants.SYSTEM_DEFAULT_USER_ID, sessionTable, "conversationContent");
        } catch (Exception e) {
            log.error("initTenantMemoryExtractTask-sessionTable error", e);
        }

        try {
            // 初始化客服对话表提取任务
            String talkTable = TenantUtils.generateKeyueRecordTableName(tenantId);
            initTenantMemoryTableTask(tenantId, Constants.SYSTEM_DEFAULT_USER_ID, talkTable, "queryText");
        } catch (Exception e) {
            log.error("initTenantMemoryExtractTask-talkTable error", e);
        }
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    @Transactional
    public void initTenantMemoryTableTask(String tenantId, String userId, String tableName, String fieldName) {
        DataTableInfo tableDetail = dataTableManageService.getTableDetailWithTableName(tableName);
        if (Objects.isNull(tableDetail)) {
            return;
        }
        TableFieldMetaInfo field = dataTableManageService.queryTableFieldMetaInfo(tableDetail.getId(), fieldName);
        if (Objects.isNull(field)) {
            return;
        }
        // 根据表 id，检查任务是否已存在
        MemoryExtract memoryExtract = selectMemoryDataTaskByDataTable(tenantId, tableDetail.getId());
        if (Objects.nonNull(memoryExtract)) {
            return;
        }

        // new task
        MemoryExtractWithBLOBs record = new MemoryExtractWithBLOBs();
        record.setTenantId(tenantId);
        record.setDataTableId(tableDetail.getId());
        record.setDatasetName(tableDetail.getCnName());
        record.setFieldId(field.getId());
        record.setFieldName(field.getCnField());
        record.setFieldNameEn(field.getEnField());
        record.setPrompt("");
        record.setPromptType(PromptTypeEnum.DEFAULT.getCode());
        record.setTriggerMod(TriggerModeEnum.REALTIME.getCode());
        record.setDescription("");
        record.setDataFilterRule("");
        record.setStatus(SwitchEnum.ON.getBoolean());
        record.setIsDefault(YesEnum.YES.getBoolean());

        Long taskId = taskInfoService.createMemoryExtractTask(TriggerModeEnum.REALTIME, null, null, userId);
        record.setTask(taskId);

        record.setDel(DelEnum.NOT_DELETED.getBoolean());
        record.setCreator(userId);
        record.setModifier(userId);
        Date now = new Date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        memoryExtractMapper.insert(record);
    }

    private MemoryExtract selectMemoryDataTaskByDataTable(String tenantId, long tableId) {
        MemoryExtractCriteria criteria = new MemoryExtractCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(tableId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<MemoryExtract> record = memoryExtractMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(record)) {
            return null;
        }
        return record.get(0);
    }
}
