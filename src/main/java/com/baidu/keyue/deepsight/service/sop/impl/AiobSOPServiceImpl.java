package com.baidu.keyue.deepsight.service.sop.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.SOPStatusEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigAssistMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigMainMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigWarningThresholdEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.sop.SOPMetaInternal;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictResponse;
import com.baidu.keyue.deepsight.models.sop.SOPProgress;
import com.baidu.keyue.deepsight.models.sop.SOPQuickNodeListResponse;
import com.baidu.keyue.deepsight.models.sop.SopBaseRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopUserDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordResp;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deepsight.models.sop.nodepredict.NodeProcessSummaryRequest;
import com.baidu.keyue.deepsight.models.sop.nodepredict.NodeProcessSummaryResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.AiobSopMetaMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopUserConfigMapper;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DataTableManageServiceImpl;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPReCalService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import io.micrometer.common.lang.Nullable;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.SQLDialect;
import org.jooq.Table;
import org.jooq.conf.ParamType;
import org.jooq.conf.Settings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;
import static org.jooq.impl.DSL.table;
import static org.jooq.impl.DSL.using;

/**
 * *@Author: dongjiacheng01
 * *@Description: 外呼sop服务接口实现类
 * *@Date: 10:31 2025/5/20
 */
@Service
@Slf4j
public class AiobSOPServiceImpl implements AiobSOPService {

    @Autowired
    @Qualifier("aiobSOPExecutor")
    private ExecutorService executorService;

    /**
     * 快捷场景 - 节点分析算子URL
     */
    @Value("${node-predict.node-summary-url}")
    private String nodePredictUrl;
    /**
     * 外呼灵活画布流程获取URL
     */
    @Value("${aiob.diagram-record-view-url}")
    private String aiobDiagramRecordViewUrl;

    /**
     * 外呼灵活画布版本获取URL
     */
    @Value("${aiob.diagram-record-url}")
    private String aiobDiagramRecordUrl;

    @Value("${aiob.url:http://127.0.0.1:8080}")
    private String aiobUrl;
    @Value("${aiob.detailPath:aiob-server/manage/task/detail}")
    private String detailPath;

    private final ObjectMapper objectMapper = new ObjectMapper();


    private final AiobSopMetaMapper sopMetaMapper;
    private final AiobSOPReCalService aiobSOPReCalService;
    private final SopUserConfigMapper sopUserConfigMapper;
    private final DorisService dorisService;
    private final DataTableManageServiceImpl tableManageService;
    private final RuleManagerService ruleManagerService;
    private final DorisConfServiceImpl dorisConfService;
    private final AiobRobotVersionService aiobRobotVersionService;

    public AiobSOPServiceImpl(AiobSopMetaMapper sopMetaMapper,
                              AiobSOPReCalService aiobSOPReCalService,
                              SopUserConfigMapper sopUserConfigMapper,
                              DorisService dorisService,
                              DataTableManageServiceImpl tableManageService,
                              RuleManagerService ruleManagerService,
                              DorisConfServiceImpl dorisConfService, AiobRobotVersionService aiobRobotVersionService) {
        this.sopMetaMapper = sopMetaMapper;
        this.aiobSOPReCalService = aiobSOPReCalService;
        this.sopUserConfigMapper = sopUserConfigMapper;
        this.dorisService = dorisService;
        this.tableManageService = tableManageService;
        this.ruleManagerService = ruleManagerService;
        this.dorisConfService = dorisConfService;
        this.aiobRobotVersionService = aiobRobotVersionService;
    }


    private List<AiobSopMeta> getSopMeta(String tenantId, String taskId, String robotVer, Boolean manualChecked) {
        AiobSopMetaCriteria sopMetaCriteria = new AiobSopMetaCriteria();
        AiobSopMetaCriteria.Criteria criteria = sopMetaCriteria.createCriteria();
        criteria.andTenantIdEqualTo(tenantId);
//        criteria.andTaskIdEqualTo(taskId);  // 同一个机器人版本的 sop 可以通用，不需要 taskId 区隔
        criteria.andVersionEqualTo(robotVer);
        if (manualChecked) {
            criteria.andManualCheckEqualTo(Boolean.TRUE);
        }
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        sopMetaCriteria.setOrderByClause("step_id asc");
        return sopMetaMapper.selectByExampleWithBLOBs(sopMetaCriteria);
    }

    @Override
    @Cacheable(value = "quickSopNodes", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public SOPQuickNodeListResponse listQuickSOPNodes(String tenantId, String taskId, String robotVer) {
        List<AiobSopMeta> aiobSopMetaList = getSopMeta(tenantId, taskId, robotVer, Boolean.TRUE);
        SOPQuickNodeListResponse response = new SOPQuickNodeListResponse();
        if (CollectionUtils.isEmpty(aiobSopMetaList)) {
            return response;
        }
        Map<String, List<SOPQuickNodeListResponse.SopQuickNodeListItem>> stepToNodeList = aiobSopMetaList.stream()
                .collect(Collectors.groupingBy(
                        AiobSopMeta::getStepName,
                        LinkedHashMap::new,
                        Collectors.mapping(SOPQuickNodeListResponse.SopQuickNodeListItem::convertFromSOPMetaEntity, Collectors.toList())
                ));
        List<SOPQuickNodeListResponse.SopQuickStepWithNodes> list = Lists.newArrayListWithCapacity(stepToNodeList.size());
        stepToNodeList.forEach((key, value) -> list.add(
                SOPQuickNodeListResponse.SopQuickStepWithNodes.builder()
                        .step(key)
                        .nodeList(value)
                        .build()
        ));
        response.setRule(aiobSopMetaList.get(0).getTaskRule());
        response.setList(list);
        return response;
    }

    @Override
    public SopSankeyMetaResponse listSOPMeta(String tenantId, String taskId, String robotVer) {
        List<AiobSopMeta> aiobSopMetaList = getSopMeta(tenantId, taskId, robotVer, Boolean.FALSE);
        if (CollectionUtils.isEmpty(aiobSopMetaList)) {
            return new SopSankeyMetaResponse(Collections.emptyList(), false);
        }

        Map<String, SOPMetaInternal> stepToMeta = aiobSopMetaList.stream().collect(Collectors.toMap(
                AiobSopMeta::getStepId,
                aiobSopMeta -> {
                    SOPMetaInternal sopMetaInternal = new SOPMetaInternal();
                    sopMetaInternal.setStepId(aiobSopMeta.getStepId());
                    sopMetaInternal.setStepName(aiobSopMeta.getStepName());
                    sopMetaInternal.setNodes(Lists.newArrayList());
                    return sopMetaInternal;
                },
                (a, b) -> a
        ));

        aiobSopMetaList.forEach(item -> {
            String stepId = item.getStepId();
            String nodeId = item.getNodeId();
            String nodeName = item.getNodeName();
            if (stepToMeta.containsKey(stepId)) {
                stepToMeta.get(stepId).getNodes().add(new SOPMetaInternal.SopMetaNode(nodeId, nodeName));
            }
        });

        List<SOPMetaInternal> stepList = stepToMeta.values().stream()
                .peek(item -> {
                    item.setNodes(item.getNodes().stream().sorted(Comparator.comparing(SOPMetaInternal.SopMetaNode::getNodeId)).toList());
                })
                .sorted(Comparator.comparing(SOPMetaInternal::getStepId))
                .collect(Collectors.toList());

        return new SopSankeyMetaResponse(stepList, aiobSopMetaList.get(0).getManualCheck());
    }

    @Override
    public SOPNodePredictResponse predictNode(String tenantId, SOPNodePredictRequest request) {
        // 若存在之前保存过的节点，直接返回
        SOPProgress sopProgress = SOPProgress.convertFromMetas(
                getSopMeta(tenantId, request.getTaskId(), request.getRobotVer(), Boolean.FALSE)
        );
        if (CollectionUtils.isNotEmpty(sopProgress.getList())) {
            return SOPNodePredictResponse.builder()
                    .isSaved(Boolean.TRUE)
                    .markdown(sopProgress.toMarkDown())
                    .build();
        }
        // 若不存在之前保存过的节点，预测
        try {
            // 1. 请求节点预测算子，总结对话规则
            NodeProcessSummaryRequest req = new NodeProcessSummaryRequest();
            req.setTaskId(request.getTaskId());
            req.setDialogueRule(request.getRule());
            NodeProcessSummaryResponse nodeSummaryResp = aiNodePredict(req);
            if (!nodeSummaryResp.success()) {
                log.error("node summary resp failed");
                throw new DeepSightException.NodePredictFailedException();
            }
            // 2. 返回解析结果
            SOPProgress response = new SOPProgress();
            response.setList(nodeSummaryResp.getResults().stream()
                    .map(NodeProcessSummaryResponse.Item::toResponseItem)
                    .toList());
            return SOPNodePredictResponse.builder()
                    .isSaved(Boolean.FALSE)
                    .markdown(response.toMarkDown())
                    .build();
        } catch (Exception e) {
            log.error("node predict service invoked failed, err: {}", e.getMessage(), e);
            throw new DeepSightException.NodePredictFailedException();
        }
    }

    public NodeProcessSummaryResponse aiNodePredict(NodeProcessSummaryRequest req) {
        log.info("node summary predict, request: {}", req);
        String resp = HttpUtil.postJsonWithTry(nodePredictUrl, JsonUtils.toJsonWithOutException(req));
        NodeProcessSummaryResponse response = JsonUtils.toObjectWithoutException(resp, NodeProcessSummaryResponse.class);
        log.info("node summary predict, response: {}", response);
        return response;
    }

    @Override
    public List<SopWholeRobotVersionResponse> listRobotVersion(SopWholeRobotVersionRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        try {
            return aiobRobotVersionService.getAiobRobotVersionList(tenantId, request.getRobotId());
        } catch (Exception e) {
            log.error("获取机器人版本列表失败: {}", e.getMessage());
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "获取机器人版本列表失败");
        }
    }

    @Override
    @Cacheable(value = "diagramRecords", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public AiobDiagramVersionRecordViewResp getDiagramRecords(String agentId, String versionId) {
        try {
            String url = aiobDiagramRecordViewUrl + "?versionId=" + versionId + "&agentId=" + agentId;
            log.info("aiob diagram version record view invoke, url: {}, agentId: {}, versionId: {}", url, agentId, versionId);
            String jsonResp = HttpUtil.getWithTry(url);
            log.info("aiob diagram version record view result is: {}", jsonResp);
            AiobDiagramVersionRecordViewResp resp = JsonUtils.toObject(jsonResp, AiobDiagramVersionRecordViewResp.class);
            if (!resp.isSuccess()) {
                log.error("aiob diagram version record view invoke failed, code: {}, msg: {}",
                        resp.getCode(), resp.getMsg());
                throw new RuntimeException(resp.getMsg());
            }
            return resp;
        } catch (Exception e) {
            log.error("aiob diagram version record view invoke failed, err: {}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public AiobDiagramVersionRecordResp getDiagramVersions(String agentId, String versionId) {
        try {
            String url = aiobDiagramRecordUrl + "?id=" + versionId + "&agentId=" + agentId + "&pageNo=0&pageSize=1";
            log.debug("aiob diagram version record invoke, url: {}, agentId: {}, versionId: {}", url, agentId, versionId);
            String jsonResp = HttpUtil.getWithTry(url);
            log.debug("aiob diagram version record result is: {}", jsonResp);
            AiobDiagramVersionRecordResp resp = JsonUtils.toObject(jsonResp, AiobDiagramVersionRecordResp.class);
            if (!resp.isSuccess()) {
                log.error("aiob diagram version record invoke failed, code: {}, msg: {}",
                        resp.getCode(), resp.getMsg());
                throw new RuntimeException(resp.getMsg());
            }
            return resp;
        } catch (Exception e) {
            log.error("aiob diagram version record view invoke failed, err: {}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void confirmNode(String tenantId, String taskId, String rule, String markdown, String robotVer) {
        // 1. 校验markdown
        SOPProgress.validMarkdown(markdown);
        // 2. 解析markdown
        SOPProgress response = SOPProgress.convertFromMarkdown(markdown);
        // 3. 删除之前保存的节点信息
        AiobSopMetaCriteria criteria = new AiobSopMetaCriteria();
        criteria.createCriteria().andTenantIdEqualTo(tenantId).andVersionEqualTo(robotVer);
        sopMetaMapper.deleteByExample(criteria);
        // 4. 保存至meta
        List<AiobSopMeta> metaList = response.toMetas(taskId, rule, robotVer);
        metaList.forEach(sopMetaMapper::insert);
        // 5. 启动节点重跑
        CompletionService<Void> completionService = new ExecutorCompletionService<>(executorService);
        completionService.submit(() -> aiobSOPReCalService.reRunQuickSOP(tenantId, robotVer));
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void updateUserConfig(String tenantId, SopUserConfigUpdateRequest request) {
        SopUserConfig userConfig = getSopUserConfig(tenantId, request.getTaskId());
        if (userConfig == null) {
            log.error("待更新用户配置不存在或无通话记录: {} ", request.getTaskId());
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "待更新用户配置不存在或无通话记录");
        }

        String userId = WebContextHolder.getUserId();
        SopUserConfigMainMetricEnum mainMetric = SopUserConfigMainMetricEnum.codeOf(request.getMainMetric());
        SopUserConfigAssistMetricEnum assistMetric = SopUserConfigAssistMetricEnum.codeOf(request.getAssistMetric());
        SopUserConfigWarningThresholdEnum threshold = SopUserConfigWarningThresholdEnum.codeOf(request.getWarningThreshold());

        userConfig.setCoreMetric(mainMetric.getCode().byteValue());
        userConfig.setAssistMetric(assistMetric.getCode().byteValue());
        userConfig.setWarningThreshold(threshold.getCode());
        userConfig.setModifier(userId);
        userConfig.setUpdateTime(new Date());

        sopUserConfigMapper.updateByPrimaryKeySelective(userConfig);
    }


    @Override
    @Transactional(rollbackOn = Exception.class)
    public SopUserConfigUpdateRequest getUserConfig(String tenantId, SopBaseRequest request) {
        String userId = WebContextHolder.getUserId();
        SopUserConfig userConfig = getOrCreateSopUserConfig(tenantId, request.getTaskId(), userId);
        SopUserConfigUpdateRequest response = new SopUserConfigUpdateRequest();
        response.setMainMetric(Integer.valueOf(userConfig.getCoreMetric()));
        response.setAssistMetric(Integer.valueOf(userConfig.getAssistMetric()));
        response.setWarningThreshold(userConfig.getWarningThreshold());
        return response;
    }

    @Nullable
    private SopUserConfig getSopUserConfig(String tenantId, String taskId) {
        SopUserConfigCriteria sopUserConfigCriteria = new SopUserConfigCriteria();
        sopUserConfigCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andTaskIdEqualTo(taskId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<SopUserConfig> userConfigs = sopUserConfigMapper.selectByExample(sopUserConfigCriteria);
        return userConfigs.isEmpty() ? null : userConfigs.get(0);
    }


    @Cacheable(value = "getOrCreateSopUserConfig", cacheManager = "aiobSopUserConfigCacheManager", key = "#tenantId + #taskId")
    @Transactional(rollbackOn = Exception.class)
    public SopUserConfig getOrCreateSopUserConfig(String tenantId, String taskId, String userId) {
        SopUserConfig userConfig = getSopUserConfig(tenantId, taskId);
        if (userConfig != null) {
            return userConfig;
        }
        Date date = new Date();
        userConfig = new SopUserConfig();
        userConfig.setTenantId(tenantId);
        userConfig.setTaskId(taskId);
        userConfig.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        userConfig.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        userConfig.setCreateTime(date);
        userConfig.setUpdateTime(date);
        userConfig.setStatus(SOPStatusEnum.INIT.getCode().byteValue());
        userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());
        userConfig.setCoreMetric(SopUserConfigMainMetricEnum.ARRIVAL_USER_NUM.getCode().byteValue());
        userConfig.setAssistMetric(SopUserConfigAssistMetricEnum.ARRIVAL_USER_NUM_RATIO.getCode().byteValue());
        userConfig.setWarningThreshold(SopUserConfigWarningThresholdEnum.PERCENT_20.getCode());
        sopUserConfigMapper.insert(userConfig);
        sopUserConfigMapper.updateByPrimaryKey(userConfig);
        return userConfig;
    }


    private void delSopUserConfig(AiobSopMeta aiobSopMeta) {
        SopUserConfig userConfig = getSopUserConfig(aiobSopMeta.getTenantId(), aiobSopMeta.getTaskId());
        if (userConfig == null) {
            return;
        }
        userConfig.setDel(DelEnum.DELETED.getBoolean());
        sopUserConfigMapper.updateByPrimaryKey(userConfig);
    }

    public BasePageResponse.Page<HashMap<String, Object>> getUserDetail(String tenantId, SopUserDetailRequest request) {
        // 根据nodeId获取sessionId
        Settings settings = new Settings().withParamType(ParamType.INLINED);
        Table<?> tNodeM = table(name(TenantUtils.generateAiobSOPNodeTableName(tenantId))).as("m");
        Table<?> tSession = table(name(TenantUtils.generateAiobSessionTableName(tenantId))).as("s");

        Field<String> taskId = field(name("m", "task_id"), String.class);
        Field<String> robotId = field(name("m", "robot_id"), String.class);
        Field<String> robotVer = field(name("m", "robot_ver"), String.class);
        Field calDate = field(name("m", "cal_date"));
        Field<String> nodeId = field(name("m", "node_id"), String.class);
        Field<String> intent = field(name("m", "intent"), String.class);
        Field<String> mSessionId = field(name("m", "sessionId"), String.class);

        Field<String> sipCode = field(name("s", "sipCode"), String.class);
        Field<String> sSessionId = field(name("s", "sessionId"), String.class);
        Field<String> startTime = field(name("s", "startTime"), String.class);
        Field<String> mobile = field(name("s", "mobile"), String.class);

        List<Condition> conditions = Lists.newArrayList();
        if (request.getIntent()) {
            // array_contains(`intent`, '有意向')
            conditions.add(intent.eq(request.getCurrNodeId()));
        } else {
            conditions.add(nodeId.eq(request.getCurrNodeId()));
        }
        if (Objects.nonNull(request.getTaskId())) {
            conditions.add(taskId.eq(request.getTaskId()));
        }
        if (Objects.nonNull(request.getRobotId())) {
            conditions.add(robotId.eq(request.getRobotId()));
        }
        if (Objects.nonNull(request.getRobotVer())) {
            conditions.add(robotVer.eq(request.getRobotVer()));
        }
        if (Objects.nonNull(request.getStartTime())) {
            conditions.add(calDate.greaterOrEqual(DatetimeUtils.formatDate(request.getStartTime())));
        }
        if (Objects.nonNull(request.getEndTime())) {
            conditions.add(calDate.lessOrEqual(DatetimeUtils.formatDate(request.getEndTime())));
        }

        // 防止空数据
        conditions.add(mobile.isNotNull().and(mobile.notEqual("")));
        // 只筛选没有挂断的
        conditions.add(sipCode.isNotNull().and(sipCode.eq("200")));
        String sql = using(SQLDialect.MYSQL, settings)
                .selectDistinct(sSessionId, startTime, mobile)
                .from(tNodeM)
                .join(tSession)
                .on(mSessionId.eq(sSessionId))
                .where(conditions.toArray(new Condition[0]))
                .limit(10000)
                .getSQL();
        if (request.getIntent()) {
            sql = StringUtils.replaceOnce(sql,
                    String.format("`m`.`intent` = '%s'", request.getCurrNodeId()),
                    String.format("array_contains(`m`.`intent`, '%s')", request.getCurrNodeId())
            );
        }
        List<Map<String, Object>> mobileMap = dorisService.selectList(sql);
        if (CollectionUtils.isEmpty(mobileMap)) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, new ArrayList<>());
        }
        // 查询现有的租户的用户表信息
        List<VisibleFieldResponse> visibleFieldResponse = tableManageService.getVisibleFields(request.getDataTableId(), false);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(tableManageService.getTableFieldByEnName(request.getDataTableId(), Constants.TABLE_MOBILE_FIELD).getId());
        filter.setType(FilterTypeEnum.STRING);
        filter.setFunction(FuncEnum.CONTAIN);
        filter.setParams(mobileMap.stream().map(map -> map.get("mobile").toString()).filter(StringUtils::isNotBlank).distinct().toList());
        if (null == request.getFilters()) {
            request.setFilters(new ArrayList<>());
        }
        Map<String, List<Map<String, Object>>> mobileGroup = mobileMap.stream()
                .collect(Collectors.groupingBy(item -> (String) item.get("mobile"),
                        Collectors.mapping(item -> {
                            Map<String, Object> newMap = new HashMap<>();
                            newMap.put("sessionId", item.get("sessionId"));
                            newMap.put("startTime", item.get("startTime"));
                            return newMap;
                        }, Collectors.toList())));
        request.getFilters().add(filter);
        RuleNode ruleNode = RuleNode.builder()
                .dataTableId(request.getDataTableId())
                .filters(request.getFilters())
                .type(RuleTypeEnum.DATASET).build();
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleNode(ruleNode);
        List<String> visibleFields = visibleFieldResponse.stream()
                .map(field -> String.format("`%s`", field.getEnName())).toList();
        long count = dorisService.getCount(dqlParseResult.parseCountSql().replace("COUNT(*)", "COUNT(DISTINCT mobile)"));
        String primaryKey = tableManageService.queryTableFieldByTag(request.getDataTableId(), TableFieldTagEnum.PRIMARY).getEnField();
        dqlParseResult.setSelect(String.join(Constants.SEPARATOR, visibleFields));
        dqlParseResult.setOrderBy(String.format("`%s`", primaryKey));
        dqlParseResult.setOffset((request.getPageNo() - 1) * request.getPageSize());
        dqlParseResult.setSize(request.getPageSize());
        String s = dqlParseResult.parseDorisSql();
        System.out.println(s);
        List<Map<String, Object>> tableContentList = dorisService.selectList(dqlParseResult.parseDorisSql());
        // 过滤重复的手机号
        List<Map<String, Object>> deduplicatedList = new ArrayList<>(
                tableContentList.stream().collect(Collectors.toMap(
                        m -> m.get("mobile"), // 以 mobile 为 key 去重
                        m -> m,               // 保留第一个出现的 map
                        (existing, replacement) -> existing)).values());
        // datetime格式转换，加密字段***输出等
        List<HashMap<String, Object>> results = deduplicatedList.stream().map(dorisData -> {
            HashMap<String, Object> map = new HashMap<String, Object>(
                    dorisConfService.dorisDataConvertToShowData(request.getDataTableId(), dorisData, visibleFieldResponse));
            map.put("sessionIdList", mobileGroup.get(dorisData.get("mobile")));
            return map;
        }).toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, results);

    }

    public List<SopUserConfig> getSopConfigByTenantId(String tenantId) {
        SopUserConfigCriteria configCriteria = new SopUserConfigCriteria();
        configCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        return sopUserConfigMapper.selectByExample(configCriteria);
    }

    public List<SopUserConfig> getSopConfigByStatus(String tenantId, Integer status) {
        SopUserConfigCriteria configCriteria = new SopUserConfigCriteria();
        configCriteria.createCriteria().andTenantIdEqualTo(tenantId)
                .andStatusNotEqualTo(status.byteValue());
        return sopUserConfigMapper.selectByExample(configCriteria);
    }

    public Integer getSopConfigStatus(String tenantId, String taskId) {
        String url = aiobUrl + "/" + detailPath;
        Map<String, String> headers = new HashMap<>();
        headers.put(Constants.ACCESS_TOKEN, Constants.ACCESS_TOKEN_VALUE);
        headers.put("Content-Type", "application/json");
        headers.put("Agentid", String.valueOf(tenantId));
        try {
            String response = HttpUtil.get(url + "?taskId=" + taskId, headers);
            if (StringUtils.isNotEmpty(response)) {
                Map<String, Object> bodyMap = objectMapper.readValue(response, Map.class);
                Map<String, Object> dataMap = (Map<String, Object>) bodyMap.get("data");
                return (Integer) dataMap.get("status");
            }
        } catch (Exception e) {
            log.error("get aiob task status error", e);
        }
        return null;
    }

    public void updateStatus(String tenantId, SOPStatusEnum status, String taskId) {
        SopUserConfigCriteria configCriteria = new SopUserConfigCriteria();
        configCriteria.createCriteria().andTenantIdEqualTo(tenantId)
                .andTaskIdEqualTo(taskId);
        SopUserConfig config = new SopUserConfig();
        config.setStatus(status.getCode().byteValue());
        sopUserConfigMapper.updateByExampleSelective(config, configCriteria);
    }

    public AiobSopMeta getFrontNodeInfoWithNodeId(String nodeId, String tenantId) {
        // 获取当前节点
        AiobSopMeta sopMeta = getNodeInfoWithNodeId(nodeId, tenantId);
        if (sopMeta.getStepId().equals("0")) {
            return sopMeta;
        }
        int stepId = Integer.parseInt(sopMeta.getStepId()) - 1;
        AiobSopMetaCriteria criteria = new AiobSopMetaCriteria();
        criteria.createCriteria()
                .andVersionEqualTo(sopMeta.getVersion())
                .andTenantIdEqualTo(tenantId)
                .andStepIdEqualTo(String.valueOf(stepId))
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<AiobSopMeta> nodes = sopMetaMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        return nodes.get(0);
    }

    public AiobSopMeta getNodeInfoWithNodeId(String nodeId, String tenantId) {
        AiobSopMetaCriteria criteria = new AiobSopMetaCriteria();
        criteria.createCriteria()
                .andNodeIdEqualTo(nodeId)
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<AiobSopMeta> nodes = sopMetaMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        return nodes.get(0);
    }

    @Override
    public BasePageResponse.Page<HashMap<String, Object>> getUserDetailV2(String tenantId, SopUserDetailRequest request) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);

        // 查询节点 oneId 总数
        String countSql;
        String querySql;
        if (request.getIntent()) {
            countSql = ORMUtils.generateCountSopUserDetailWithIntentSQL(
                    nodeMetricTableName, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getStartTime(), request.getEndTime(), request.getCurrNodeId());
            querySql = ORMUtils.generateQuerySopUserDetailWithIntentSQL(
                    nodeMetricTableName, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getStartTime(),
                    request.getEndTime(), request.getCurrNodeId(), request.getPageNo(), request.getPageSize());
        } else {
            countSql = ORMUtils.generateCountSopUserDetailSQL(
                    nodeMetricTableName, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getStartTime(), request.getEndTime(), request.getCurrNodeId());
            querySql = ORMUtils.generateQuerySopUserDetailSQL(
                    nodeMetricTableName, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getStartTime(),
                    request.getEndTime(), request.getCurrNodeId(), request.getPageNo(), request.getPageSize());
        }

        long total = 0L;
        try {
            total = getUserDetailCount(countSql);
        } catch (Exception e) {
            log.error("AiobSOPService.getUserDetailV2 do count error, sql: {}, err: ", countSql, e);
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, new ArrayList<>());
        }

        // 分页查询oneID，及对应的sessionIDs
        List<Map<String, Object>> recordList;
        try {
            recordList = getUserNodeSessionIds(querySql);
        } catch (Exception e) {
            log.error("AiobSOPService.getUserDetailV2 do query error, sql: {}, err: ", querySql, e);
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), total, new ArrayList<>());
        }

        HashSet<String> sessionIdSet = new HashSet<>();
        recordList.forEach(record -> {
            String sessionIds = (String) record.get("sessionIds");
            if (StringUtils.isBlank(sessionIds)) {
                return;
            }
            List<String> sessionIdList = Lists.newArrayList(sessionIds.split(","));
            sessionIdSet.addAll(sessionIdList);
        });

        Map<String, List<String>> oneIdAndMobileToSessionIds = new HashMap<>();
        Map<String, Map<String, String>> sessionToStartTimeMap = querySessionStartTime(tenantId, sessionIdSet);
        Set<String> oneIdSet = new HashSet<>();
        Set<String> mobileSet = new HashSet<>();
        sessionToStartTimeMap.forEach((sessionId, value) -> {
            String oneId = value.get("oneId");
            String mobile = value.get(Constants.TABLE_MOBILE_FIELD);
            oneIdAndMobileToSessionIds.computeIfAbsent(oneId, k -> new ArrayList<>()).add(sessionId);
            oneIdAndMobileToSessionIds.computeIfAbsent(mobile, k -> new ArrayList<>()).add(sessionId);
            oneIdSet.add(oneId);
            mobileSet.add(mobile);
        });

        if (oneIdSet.isEmpty() && mobileSet.isEmpty()) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), total, new ArrayList<>());
        }

        // 查询用户信息
        List<VisibleFieldResponse> visibleFieldResponse = tableManageService.getVisibleFields(request.getDataTableId(), false);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        List<Map<String, Object>> mobileContentList = queryUserContent(request, mobileSet, visibleFieldResponse, Constants.TABLE_MOBILE_FIELD);
        List<Map<String, Object>> oneIdContentList = queryUserContent(request, oneIdSet, visibleFieldResponse, Constants.TABLE_USER_ONE_ID);
        List<Map<String, Object>> tableContentList = mergeDeduplication(oneIdContentList, mobileContentList);
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), total,
                constructUserDetail(tableContentList, visibleFieldResponse, request.getDataTableId(), oneIdAndMobileToSessionIds, sessionToStartTimeMap));
    }

    /**
     * 合并&去重
     * 入口数据为根据mobile和oneId查询数据，可能存在重复用户
     * 同一个mobile可能查询到多个用户
     * 根据userid进行去重
     * 根据手机号去重
     * @param oneIdContentList oneId查询结果
     * @param mobileContentList mobile查询结果
     * @return 去重结果
     */
    public List<Map<String, Object>> mergeDeduplication(List<Map<String, Object>> oneIdContentList, List<Map<String, Object>> mobileContentList) {
        boolean oneIdEmpty = CollUtil.isEmpty(oneIdContentList);
        boolean mobileEmpty = CollUtil.isEmpty(mobileContentList);
        // 均为空，直接返回
        if (oneIdEmpty && mobileEmpty) {
            return Collections.emptyList();
        }
        // 手机号去重
        if (!mobileEmpty) {
            Set<String> mobileSet = new HashSet<>();
            mobileContentList = mobileContentList.stream().filter(data -> {
                Object mobileObj = data.get(Constants.TABLE_MOBILE_FIELD);
                String mobileStr = mobileObj == null ? null : mobileObj.toString();
                if (StrUtil.isBlank(mobileStr)) {
                    return true;
                }
                if (mobileSet.contains(mobileStr)) {
                    return false;
                }
                mobileSet.add(mobileStr);
                return true;
            }).toList();
        }
        // 其中一个为空，返回不为空结果
        if (oneIdEmpty || mobileEmpty) {
            return oneIdEmpty ? mobileContentList : oneIdContentList;
        }
        // 合并去重
        Map<String, Map<String, Object>> map = new HashMap<>();
        oneIdContentList.forEach(data -> map.put(data.get(Constants.TABLE_USER_USER_ID).toString(), data));
        mobileContentList.forEach(data -> map.put(data.get(Constants.TABLE_USER_USER_ID).toString(), data));
        return map.values().stream().toList();
    }

    public Map<String, Map<String, String>> querySessionStartTime(String tenantId, Set<String> sessionIds) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return Collections.emptyMap();
        }
        String sessionTable = TenantUtils.generateAiobSessionTableName(tenantId);
        String sql = ORMUtils.generateQuerySessionStartTimeWithIdsSQL(sessionTable, Lists.newArrayList(sessionIds));
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSOPService.querySessionStartTime do query error, sql: {}, err: ", sql, e);
            return Collections.emptyMap();
        }
        HashMap<String, Map<String, String>> sessionStartTimeMap = new HashMap<>();
        recordList.forEach(record -> {
            Map<String, String> data = new HashMap<>();
            data.put("sessionId", (String) record.get("sessionId"));
            data.put("startTime", String.valueOf(record.get("startTime")));
            data.put("mobile", (String) record.get("mobile"));
            data.put("oneId", (String) record.get("oneId"));
            sessionStartTimeMap.put((String) record.get("sessionId"), data);
        });

        return sessionStartTimeMap;
    }

    public List<Map<String, Object>> queryUserContent(SopUserDetailRequest request,
                                                      Set<String> valueSet,
                                                      List<VisibleFieldResponse> visibleFieldResponse, 
                                                      String fieldName) {
        List<RuleFilter> filters = new ArrayList<>();
        RuleFilter filter = new RuleFilter();
        filter.setFieldId(tableManageService.getTableFieldByEnName(request.getDataTableId(), fieldName).getId());
        filter.setType(FilterTypeEnum.STRING);
        filter.setFunction(FuncEnum.CONTAIN);
        filter.setParams(Lists.newArrayList(valueSet));
        filters.add(filter);
        if (CollUtil.isNotEmpty(request.getFilters())) {
            filters.addAll(request.getFilters());
        }
        RuleNode ruleNode = RuleNode.builder()
                .dataTableId(request.getDataTableId())
                .filters(filters)
                .type(RuleTypeEnum.DATASET).build();
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleNode(ruleNode);

        List<String> visibleFields = visibleFieldResponse.stream()
                .map(field -> String.format("`%s`", field.getEnName()))
                .collect(Collectors.toList());
        visibleFields.add(String.format("`%s`", Constants.TABLE_USER_ONE_ID));
        visibleFields.add(String.format("`%s`", Constants.TABLE_USER_USER_ID));
        visibleFields.add(String.format("`%s`", Constants.TABLE_MOBILE_FIELD));
        visibleFields = visibleFields.stream().distinct().collect(Collectors.toList());

        String primaryKey = tableManageService.queryTableFieldByTag(request.getDataTableId(), TableFieldTagEnum.PRIMARY).getEnField();
        dqlParseResult.setSelect(String.join(Constants.SEPARATOR, visibleFields));
        dqlParseResult.setOrderBy(String.format("`%s`", primaryKey));
        List<Map<String, Object>> tableContentList = new ArrayList<>();
        String parseDorisSql = dqlParseResult.parseDorisSql();
        try {
            tableContentList = dorisService.selectList(parseDorisSql);
        } catch (Exception e) {
            log.error("AiobSOPService.queryUserContent do query error, sql: {}, err: ", parseDorisSql, e);
        }
        return tableContentList;
    }

    public Long getUserDetailCount(String countSql) {
        long total = 0L;
        try {
            total = dorisService.getCount(countSql);
        } catch (Exception e) {
            log.error("AiobSOPService.getUserDetailV2 do count error, sql: {}, err: ", countSql, e);
        }
        return total;
    }

    public List<Map<String, Object>> getUserNodeSessionIds(String querySql) {
        List<Map<String, Object>> recordList = Lists.newArrayList();
        try {
            recordList = dorisService.selectList(querySql);
        } catch (Exception e) {
            log.error("AiobSOPService.getUserDetailV2 do query error, sql: {}, err: ", querySql, e);
        }
        return recordList;
    }

    public List<HashMap<String, Object>> constructUserDetail(List<Map<String, Object>> tableContentList, List<VisibleFieldResponse> visibleFieldResponse,
                                                             Long dataTableId, Map<String, List<String>> oneIdToSessionIds,
                                                             Map<String, Map<String, String>> sessionToStartTimeMap) {
        return tableContentList.stream()
                .collect(Collectors.toMap(
                        record -> record.get((String) Constants.TABLE_USER_ONE_ID),
                        record -> record,
                        (v1, v2) -> v1)
                ).values().stream()
                .map(dorisData -> {
                    HashMap<String, Object> map = new HashMap<>(dorisConfService.dorisDataConvertToShowData(dataTableId, dorisData, visibleFieldResponse));
                    String oneId = (String) dorisData.get(Constants.TABLE_USER_ONE_ID);
                    String mobile = (String) dorisData.get(Constants.TABLE_MOBILE_FIELD);
                    List<String> sessionIdList = oneIdToSessionIds.get(oneId);
                    if (CollUtil.isEmpty(sessionIdList) && CollUtil.isNotEmpty(oneIdToSessionIds.get(mobile))) {
                        sessionIdList = oneIdToSessionIds.get(mobile);
                    }

                    List<Map<String, String>> sessionIdContents = sessionIdList.stream()
                            .filter(sessionToStartTimeMap::containsKey)
                            .map(sessionId -> {
                                Map<String, String> sessionIdContent = new HashMap<>();
                                sessionIdContent.put("sessionId", sessionId);
                                sessionIdContent.put("startTime", sessionToStartTimeMap.get(sessionId).get("startTime"));
                                return sessionIdContent;
                            }).collect(Collectors.toList());

                    map.put("sessionIdList", sessionIdContents);
                    return map;
                })
                .sorted(Comparator.comparing(record -> (String) record.get(Constants.TABLE_USER_ONE_ID)))
                .toList();
    }
}
