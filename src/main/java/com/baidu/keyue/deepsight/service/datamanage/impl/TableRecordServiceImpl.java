package com.baidu.keyue.deepsight.service.datamanage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.sax.handler.RowHandler;
import cn.hutool.poi.exceptions.POIException;
import com.alibaba.fastjson.JSONObject;
import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.ESToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.FileTypeEnum;
import com.baidu.keyue.deepsight.enums.ImportMappingTypeEnum;
import com.baidu.keyue.deepsight.enums.ImportStatusEnum;
import com.baidu.keyue.deepsight.enums.ImportTypeEnum;
import com.baidu.keyue.deepsight.enums.MysqlToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldMappingDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.RowDataDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableRecordMsgDTO;
import com.baidu.keyue.deepsight.models.datamanage.dto.TaskFileImportDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.UploadDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportFieldMappingRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportTaskDeleteRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportTaskListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDataCurlRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldAiMappingResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskDeleteResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskQueryResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileSaveResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableSyncDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.exception.ServiceException;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImport;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.TaskFileImportMapper;
import com.baidu.keyue.deepsight.service.ai.AiBaseService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.FileUtil;
import com.baidu.keyue.deepsight.utils.FreeMarkUtil;
import com.baidu.keyue.deepsight.utils.GzipUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.baidubce.services.bos.model.BosObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.jetbrains.annotations.NotNull;
import org.mortbay.util.ajax.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className TableRecordServiceImpl
 * @description 数据表内容同步
 * @date 2025/1/8 16:11
 */
@Service
@Slf4j
public class TableRecordServiceImpl implements TableContentService {

    @Autowired
    @Qualifier("tableContentExecutor")
    private ExecutorService executorService;

    @Autowired
    private AccessTokenService tokenService;

    @Autowired
    private TableRecordCommonService tableRecordCommonService;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private RestHighLevelClient client;

    @Resource
    private TaskFileImportMapper taskFileImportMapper;

    @Resource
    private BosUtils bosUtils;

    @Resource
    private DorisService dorisService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private DataTableManageService dataTableManageService;

    @Resource
    private DorisConfiguration dorisConfiguration;

    @Resource
    private AiBaseService aiBaseService;

    @Resource
    private BosConfig bosConfig;

    @Value("${kafka.topics.dataSync:deep_sight_data_sync}")
    private String topic;

    @Value("${data.sync.url:http://rec-gateway-admin:8080}")
    private String url;

    @Value("${local.file.maxBatch:3}")
    private Integer maxBatch;

    @Value("${local.file.tempFilePath:tempFile}")
    private String tempFilePath;

    @Value("${file.upload.suffixNames}")
    private String suffixNames;

    @Value("${file.upload.enFiledPattern}")
    private String enFieldPattern;


    @Value("${spring.data.elasticsearch.prefix:dev_}")
    private String indexPrefix;

    private final Set<String> suffixNameSet = new HashSet<>();
    private ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
        if (StrUtil.isNotBlank(suffixNames)) {
            suffixNameSet.addAll(Arrays.asList(suffixNames.split(",")));
        }
    }

    @Override
    public void handleBatchSync(List<Map<String, Object>> itemMaps, String dataTableId) {
        itemMaps.stream().forEach(itemMap -> {
            // oneId去除
            itemMap.remove("oneId");
            handleSingleSync(itemMap, dataTableId);
        });
    }

    private void handleSingleSync(Map<String, Object> itemMap, String dataTableId) {
        try {
            Long id = Long.valueOf(dataTableId);
            DataTableInfo info = tableRecordCommonService.getDataTableDetail(id);
            // 必填项&数据类型校验&敏感数据加密
            tableRecordCommonService.recordCheck(itemMap,
                    tableRecordCommonService.getRequiredFields(id),
                    tableRecordCommonService.getFieldsType(id), info.getDbType());
            // 敏感数据加密
            tableRecordCommonService.encryptItem(itemMap, tableRecordCommonService.getEncryptFields(id));
            TableFieldMetaInfo metaInfo = tableRecordCommonService.queryTableFieldByTag(id, TableFieldTagEnum.PRIMARY);
            // 根据db类型，写入数据
            syncDataByDbType(info.getDbType(), itemMap, info.getTableName(), metaInfo.getEnField());
        } catch (DeepSightException.ParamsErrorException e) {
            log.error("data sync error! dataTableId is {}, itemMaps is {}", dataTableId, JSON.toString(itemMap), e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "参数校验失败:" + e.getMessage());
        } catch (Exception e) {
            log.error("data sync error! dataTableId is {}, itemMaps is {}", dataTableId, JSON.toString(itemMap), e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据接入失败");
        }

    }

    @Override
    public TableSyncDetailResponse getSyncInfo(GetTableDataCurlRequest request) {
        TableSyncDetailResponse response = new TableSyncDetailResponse();
        DataTableInfo dataTableInfo = dataTableManageService.validDataTableByTenantId(request.getDataTableId());
        String token = tokenService.getTableToken(dataTableInfo.getTableName(), Long.valueOf(dataTableInfo.getTenantid()));
        // 构建样例请求
        List<TableFieldMetaInfo> fieldMetaInfos = dataTableManageService.queryTableFieldsMetaInfo(request.getDataTableId());
        if (CollectionUtils.isEmpty(fieldMetaInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请检查dataTableId");
        }
        Map<String, Object> exampleData = new HashMap<>();
        if (dataTableInfo.getDbType().equals(DbTypeEnum.DORIS_TYPE.getDbType())) {
            exampleData = fieldMetaInfos.stream()
                    .filter(metaInfo -> metaInfo.getIsRequired())
                    .collect(Collectors.toMap(TableFieldMetaInfo::getEnField, value -> {
                        MysqlToJavaTypeMapping dataType = MysqlToJavaTypeMapping.getJavaType(value.getDataType());
                        switch (dataType) {
                            case VARCHAR:
                                return "data";
                            case JSON:
                                return new JSONObject();
                            case ARRAY:
                                return new String[]{"test01"};
                            case DATETIME:
                                LocalDateTime now = LocalDateTime.now();
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                return now.format(formatter);
                            case INT, TINYINT, BIGINT:
                                return 1;
                            case BOOLEAN:
                                return true;
                            default:
                                return "null";
                        }
                    }));
        } else if (dataTableInfo.getDbType().equals(DbTypeEnum.ES_TYPE.getDbType())) {
            exampleData = fieldMetaInfos.stream()
                    .filter(metaInfo -> metaInfo.getIsRequired())
                    .collect(Collectors.toMap(TableFieldMetaInfo::getEnField, value -> {
                        ESToJavaTypeMapping dataType = ESToJavaTypeMapping.getJavaType(value.getDataType());
                        switch (dataType) {
                            case INT:
                                return 1;
                            case FLOAT:
                                return 1.0;
                            case STING:
                                return "data";
                            case STINGS:
                                return new String[]{"test01"};
                            case DATE:
                                LocalDateTime now = LocalDateTime.now();
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                return now.format(formatter);
                            case BOOLEAN:
                                return true;
                            default:
                                return "null";
                        }
                    }));
        }
        // 获取python调用示例
        String pyContent = getSyncContent(dataTableInfo.getId(), token, "python", JSONObject.toJSONString(exampleData));
        // 获取java调用示例
        String javaContent = getSyncContent(dataTableInfo.getId(), token, "java", JSONObject.toJSONString(exampleData));
        // 获取curl调用示例
        String curlContent = getCurlContent(dataTableInfo.getId(), token, JSONObject.toJSONString(exampleData));
        response.setCurlContent(curlContent);

        response.setJavaContent(javaContent);
        response.setPythonContent(pyContent);
        response.setUrl(url + "/deepsight/v1/table/content/sync");
        response.setTableName(dataTableInfo.getTableName());
        response.setAuth(token);
        return response;


    }

    private void syncDataByDbType(String dbType, Map<String, Object> itemMap, String tableName, String primaryKey)
            throws Exception {
        if (DbTypeEnum.DORIS_TYPE.getDbType().equals(dbType)) {
            TableRecordMsgDTO recordMsg = new TableRecordMsgDTO();
            recordMsg.setCode(tableName);
            recordMsg.setData(itemMap);
            String msg = objectMapper.writeValueAsString(recordMsg);
            log.debug("send data sync mq, msg is {}", msg);
            kafkaTemplate.send(topic, msg);
        } else if (DbTypeEnum.ES_TYPE.getDbType().equals(dbType)) {
            tableName = indexPrefix + tableName;
            IndexRequest request = new IndexRequest(tableName)
                    .id(String.valueOf(itemMap.get(primaryKey)))
                    .source(itemMap);
            IndexResponse response = client.index(request, RequestOptions.DEFAULT);

        }


    }

    private String getCurlContent(Long dataTableId, String token, String body) {
        return "curl --location " +
                "--request POST '" + url + "/deepsight/v1/table/content/sync" + "' " +
                "--header 'Authorization: " + token + "' " +
                "--header 'Content-Type: application/json' " +
                "--data-raw '[" + body + "]'";
    }

    private String getSyncContent(Long dataTableId, String token, String type, String body) {
        // 读取模板文件
        String templateFile = "datamanage/sync_" + type + ".temp";
        String templateContent = null;
        String content = null;
        try {
            templateContent = FileUtil.readFileAsString(templateFile);
        } catch (IOException e) {
            log.error("ReadFileAsString occurred exception", e);
        }
        Map<String, Object> values = new HashMap<String, Object>() {{
            put("url", url + "/deepsight/v1/table/content/sync");
            put("Authorization", token);
            if (("java").equals(type)) {
                put("samples", StringEscapeUtils.escapeJava(JSON.toString(Arrays.asList(body))));
            } else {
                put("samples", body);
            }
        }};
        try {
            content = FreeMarkUtil.stringReplace(templateContent, values);
        } catch (Exception e) {
            log.error(" render template occurred exception", e);
        }
        return content;

    }


    @Override
    @Transactional
    public FileSaveResponse fileSave(FileImportSaveRequest request, UserAuthInfo userAuthInfo) {
        Long dataTableId = request.getDataTableId();
        String groupId = request.getGroupId();
        String userId = String.valueOf(userAuthInfo.getUserId());
        List<FileDetailDto> files = request.getFiles();
        String userName = userAuthInfo.getUserName();
        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        // 首次上传，生成分组ID
        if (StrUtil.isBlank(groupId)) {
            groupId = IdUtil.fastUUID().replaceAll("-", "");
        }
        // 校验表头数据，并获取第一个文件的前三行数据：字段英文名、字段中文名、业务数据
        List<RowDataDto> rowData = checkFileData(files, dataTableId, groupId, tenantId);
        FileSaveResponse response = new FileSaveResponse();
        response.setRowData(rowData);
        List<FileSaveResponse.SaveDetail> details = new ArrayList<>();
        for (FileDetailDto file : files) {
            String originalFilename = file.getFileName();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            // 上传记录到MySQL
            TaskFileImportWithBLOBs taskFileImport = newTaskFileImport(userId, tenantId, originalFilename, file.getBosKey(),
                    fileSuffixName, rowData, dataTableId, userName, groupId);
            taskFileImportMapper.insert(taskFileImport);
            details.add(new FileSaveResponse.SaveDetail(taskFileImport.getId(), taskFileImport.getSourceName()));
        }
        response.setUploadDetails(details);
        response.setGroupId(groupId);
        return response;
    }

    /**
     * 检查上传文件数据
     * 1、检查分组ID下是上传文件数+当前文件数是否超过做大限制
     * 2、检查新上传文件的表头是否一致
     * 3、检查新上传文件表头与库中同组文件表头是否一致
     *
     * @param files       上传文件
     * @param dataTableId 数据集ID
     * @param groupId     任务组ID
     * @param tenantId    租户ID
     * @return 首行数据
     */
    public List<RowDataDto> checkFileData(List<FileDetailDto> files, Long dataTableId, String groupId, String tenantId) {
        // 文件后缀校验
        Set<String> fileNames = new HashSet<>();
        for (FileDetailDto file : files) {
            String originalFilename = file.getFileName();
            // 同批次重复校验
            if (fileNames.contains(originalFilename)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "同一个数据集文件名重复:" + originalFilename);
            }
            fileNames.add(originalFilename);
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            if (!suffixNameSet.contains(fileSuffixName)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "文件扩展名错误，仅支持:" + suffixNames);
            }
        }
        TaskFileImportCriteria criteria = new TaskFileImportCriteria();
        TaskFileImportCriteria.Criteria criteriaCriteria = criteria.createCriteria();
        criteriaCriteria.andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(dataTableId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<TaskFileImportWithBLOBs> list = taskFileImportMapper.selectByExampleWithBLOBs(criteria);
        List<TaskFileImportDto> oldTasks = new ArrayList<>();
        for (TaskFileImportWithBLOBs task : list) {
            if (Objects.equals(task.getGroupId(), groupId)) {
                oldTasks.add(new TaskFileImportDto(task));
            }
            // 统数据集文件名重复校验
            if (fileNames.contains(task.getSourceName())) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "同一个数据集文件名重复:" + task.getSourceName());
            }
        }
        // 单次任务上传文件数校验
        if (CollUtil.isNotEmpty(oldTasks) && (oldTasks.size() + files.size()) > maxBatch) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, String.format("单次最大支持%s个文件上传", maxBatch));
        }
        // 表头校验
        List<RowDataDto> oldRow = CollUtil.isNotEmpty(oldTasks) ? oldTasks.get(0).getRowData() : null;
        for (FileDetailDto file : files) {
            List<RowDataDto> oneData = getOneDataFromFile(file);
            // 表头字段数量校验
            Assert.isTrue(CollUtil.isEmpty(oldRow) || oldRow.size() == oneData.size(),
                    () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "上传文件表头字段数量不一致"));
            // 与已上传的同组任务中英文字段名以及顺序对比
            if (CollUtil.isNotEmpty(oldRow)) {
                for (int i = 0; i < oldRow.size(); i++) {
                    RowDataDto oldR = oldRow.get(i);
                    RowDataDto newR = oneData.get(i);
                    Assert.isTrue(Objects.equals(oldR.getEnName(), newR.getEnName()), () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "上传文件表头英文字段名不一致"));
                    Assert.isTrue(Objects.equals(oldR.getCnName(), newR.getCnName()), () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "上传文件表头中文字段名不一致"));
                }
            }
            oldRow = oneData;
        }
        return oldRow;
    }

    /**
     * 创建导入任务实体
     *
     * @param userId           用户ID
     * @param tenantId         租户ID
     * @param originalFilename 文件名
     * @param newFileName      BOS文件名
     * @param fileSuffixName   文件后缀名
     * @param rowData          第一行数据
     * @param dataTableId      数据集ID
     * @param userName         操作者姓名
     * @param groupId          任务组ID
     * @return
     */
    @NotNull
    public static TaskFileImportWithBLOBs newTaskFileImport(String userId,
                                                            String tenantId,
                                                            String originalFilename,
                                                            String newFileName,
                                                            String fileSuffixName,
                                                            List<RowDataDto> rowData,
                                                            Long dataTableId,
                                                            String userName,
                                                            String groupId) {
        Date now = new Date();
        TaskFileImportWithBLOBs taskFileImport = new TaskFileImportWithBLOBs();
        taskFileImport.setSourceName(originalFilename);
        taskFileImport.setBosName(newFileName);
        taskFileImport.setCreator(userId);
        taskFileImport.setModifier(userId);
        taskFileImport.setTenantId(tenantId);
        taskFileImport.setCreateTime(now);
        taskFileImport.setUpdateTime(now);
        taskFileImport.setStatus(ImportStatusEnum.UPLOAD.getStatus());
        taskFileImport.setDel(false);
        taskFileImport.setSuffixName(fileSuffixName);
        taskFileImport.setFirstRowData(GzipUtils.compressObj(rowData));
        taskFileImport.setDataTableId(dataTableId);
        taskFileImport.setOperatorName(userName);
        taskFileImport.setGroupId(groupId);
        return taskFileImport;
    }

    @Override
    public FileImportTaskResponse fileImport(FileImportRequest fileImportRequest, UserAuthInfo userAuthInfo) {
        String userId = String.valueOf(userAuthInfo.getUserId());
        String userName = userAuthInfo.getUserName();
        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        Long dataTableId = fileImportRequest.getDataTableId();
        DataTableInfo tableDetail = tableRecordCommonService.getDataTableDetail(dataTableId);
        if (Objects.isNull(tableDetail) || !tableDetail.getTenantid().equals(tenantId)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "数据集不存在");
        }
        TaskFileImportCriteria criteria = new TaskFileImportCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andStatusEqualTo(ImportStatusEnum.UPLOAD.getStatus())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andGroupIdEqualTo(fileImportRequest.getGroupId());
        List<TaskFileImportWithBLOBs> taskList = taskFileImportMapper.selectByExampleWithBLOBs(criteria);
        if (CollUtil.isEmpty(taskList)) {
            log.error("任务为空：tenantId:{}, {}", tenantId, JSONUtil.toJsonStr(fileImportRequest));
        }
        Assert.notEmpty(taskList, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务不存在或已删除:" + (tenantId == null ? "null" : tenantId)));
        // 校验表信息和覆盖预处理，覆盖式写入则先删除表中数据
        if (ImportTypeEnum.COVER.equals(fileImportRequest.getImportTypeEnum())) {
            String dbType = tableDetail.getDbType();
            if (Objects.equals(DbTypeEnum.DORIS_TYPE.getDbType(), dbType)) {
                String sql = String.format("DELETE FROM `%s`.`%s` WHERE 1=1;", dorisConfiguration.getDb(), tableDetail.getTableName());
                log.warn("execSql delete table data:{}", sql);
                dorisService.execSql(sql);
            } else {
                String indexName = indexPrefix + tableDetail.getTableName();
                DeleteByQueryRequest request = new DeleteByQueryRequest(indexName);
                // 删除所有文档
                request.setQuery(QueryBuilders.matchAllQuery());
                // 忽略版本冲突
                request.setConflicts("proceed");
                // 设置超时时间
                request.setTimeout(TimeValue.timeValueMinutes(2));
                // 删除后刷新索引
                request.setRefresh(true);
                try {
                    client.deleteByQuery(request, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    log.error("delete es index data error:", e);
                    throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "清除ES数据异常");
                }
            }
        }
        // 压缩字段映射配置
        List<FieldMappingDto> fieldMappings = fileImportRequest.getFieldMappings();
        byte[] mappingByte = GzipUtils.compressObj(fieldMappings);
        if (mappingByte == null || mappingByte.length == 0) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "字段映射配置转JSON字符串异常");
        }
        FileImportTaskResponse res = new FileImportTaskResponse();
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>();
        Date now = new Date();
        // 使用UUID作为分组ID
        for (TaskFileImportWithBLOBs taskFileImport : taskList) {
            // 构建导入数据
            taskFileImport.setStatus(ImportStatusEnum.IMPORTING.getStatus());
            taskFileImport.setOperatorName(userName);
            taskFileImport.setUpdateTime(now);
            taskFileImport.setDataTableId(fileImportRequest.getDataTableId());
            taskFileImport.setModifier(userId);
            taskFileImport.setFieldMapping(mappingByte);
            taskFileImport.setWriteType(fileImportRequest.getImportTypeEnum().getValue());
            taskFileImport.setMappingType(fileImportRequest.getMappingTypeEnum().getValue());
            // 添加导入数据异步任务
            futures.add(CompletableFuture.supplyAsync(() -> importFileData(taskFileImport, fileImportRequest, res, tableDetail)));
        }
        TableContentService tableContentService = applicationContext.getBean(TableContentService.class);
        tableContentService.updateImportTasksById(taskList);
        List<Map<String, Object>> errors = new ArrayList<>();
        // 异步执行数据导入
        futures.stream().map(CompletableFuture::join).filter(Objects::nonNull).forEach(errors::addAll);
        if (CollUtil.isNotEmpty(errors)) {
            TaskFileImportWithBLOBs firstJob = taskList.get(0);
            String sourceName = firstJob.getSourceName();
            String name = sourceName.substring(0, sourceName.lastIndexOf("."));
            String pureDate = DatetimeUtils.pureDateFormat(new Date());
            String errorBosFileName = String.format("/%s/%s/error_%s.xlsx", bosConfig.getEnv(), pureDate, name);
            // 写出错误数据Excel到BOS
            uploadErrorExcel(errors, errorBosFileName, res, taskList);
            // 刷新任务表错误数据BOS文件key
            tableContentService.updateImportTasksById(taskList);
        }
        return res;
    }

    /**
     * 上传导入失败数据Excel到BOS
     *
     * @param errors           错误数据
     * @param errorBosFileName BOS文件名KEY
     * @param res              导入数据响应
     * @param taskList         任务列表
     */
    private void uploadErrorExcel(List<Map<String, Object>> errors,
                                  String errorBosFileName,
                                  FileImportTaskResponse res,
                                  List<TaskFileImportWithBLOBs> taskList) {
        try {
            File pathFile = new File(tempFilePath);
            if (!pathFile.exists()) {
                boolean mkdir = pathFile.mkdir();
                Assert.isTrue(mkdir);
            }
            String filePath = String.format("%s/%s", tempFilePath, errorBosFileName);
            File file = new File(filePath);
            ExcelWriter writer = ExcelUtil.getWriter(file);
            writer.write(errors, true);
            writer.close();
            // 上传BOS
            bosUtils.putObject(bosConfig.getBucket().getDataSync(), errorBosFileName, new FileInputStream(file));
            // 更新任务记录
            taskList.forEach(task -> task.setBosErrName(errorBosFileName));
            // 删除临时表数据
            file.delete();
        } catch (Exception e) {
            res.setMessage("写出导入数据错误Excel异常");
            log.error("写出导入数据错误Excel异常", e);
        }
    }

    @Override
    public List<FileImportTaskQueryResponse> fileImportTaskList(FileImportTaskListRequest request, UserAuthInfo userAuthInfo) {
        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        TaskFileImportCriteria criteria = new TaskFileImportCriteria();
        TaskFileImportCriteria.Criteria criteriaCriteria = criteria.createCriteria();
        criteriaCriteria.andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(request.getDataTableId())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        FileTypeEnum fileTypeEnum = request.getFileTypeEnum();
        if (fileTypeEnum != null) {
            criteriaCriteria.andSuffixNameEqualTo(fileTypeEnum.getValue());
        }
        List<TaskFileImportWithBLOBs> taskFileImports = taskFileImportMapper.selectByExampleWithBLOBs(criteria);
        if (CollUtil.isEmpty(taskFileImports)) {
            return Collections.emptyList();
        }
        // 映射DTO
        List<FileImportTaskQueryResponse> responseList = new ArrayList<>();
        Map<String, List<TaskFileImportDto>> collect = taskFileImports.stream()
                .filter(task -> !Objects.isNull(task.getGroupId()))
                .map(TaskFileImportDto::new)
                .collect(Collectors.groupingBy(TaskFileImportDto::getGroupId));
        for (Map.Entry<String, List<TaskFileImportDto>> entry : collect.entrySet()) {
            responseList.add(new FileImportTaskQueryResponse(entry.getKey(), entry.getValue()));
        }
        // 卡片时间降序
        responseList.sort((task1, task2) -> (int) (task2.getCreateTime().getTime() - task1.getCreateTime().getTime()));
        return responseList;
    }


    @Override
    @Transactional
    public void updateImportTasksById(List<TaskFileImportWithBLOBs> taskList) {
        if (CollUtil.isNotEmpty(taskList)) {
            taskList.forEach(task -> taskFileImportMapper.updateByPrimaryKeyWithBLOBs(task));
        }
    }

    @Override
    @Transactional
    public FileImportTaskDeleteResponse fileImportTaskDelete(FileImportTaskDeleteRequest deleteRequest, UserAuthInfo userAuthInfo) {
        String userId = String.valueOf(userAuthInfo.getUserId());
        String userName = userAuthInfo.getUserName();
        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        TaskFileImportCriteria criteria = new TaskFileImportCriteria();
        TaskFileImportCriteria.Criteria criteriaCriteria = criteria.createCriteria();
        criteriaCriteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(deleteRequest.getDataTableId());
        // 支持根据任务组ID或文件ids删除
        if (StrUtil.isNotBlank(deleteRequest.getGroupId())) {
            criteriaCriteria.andGroupIdEqualTo(deleteRequest.getGroupId());
        } else if (CollUtil.isNotEmpty(deleteRequest.getFileIds())) {
            criteriaCriteria.andIdIn(deleteRequest.getFileIds());
        } else {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务组ID和文件ids不能同时为空");
        }
        List<TaskFileImportWithBLOBs> list = taskFileImportMapper.selectByExampleWithBLOBs(criteria);
        Assert.notEmpty(list, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务不存在或已删除"));
        Integer importing = ImportStatusEnum.IMPORTING.getStatus();
        Date now = new Date();
        Set<String> deleteKeys = new HashSet<>();
        for (TaskFileImportWithBLOBs task : list) {
            task.setDel(DelEnum.DELETED.getBoolean());
            task.setUpdateTime(now);
            task.setModifier(userId);
            task.setOperatorName(userName);
            // 校验任务状态，包含导入中的则不能删除
            Assert.notEquals(importing, task.getStatus(), () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "导入中任务不能删除"));
            deleteKeys.add(task.getBosName());
        }
        updateImportTasksById(list);
        String dataSync = bosConfig.getBucket().getDataSync();
        deleteKeys.forEach(name -> bosUtils.deleteObject(dataSync, name));
        return new FileImportTaskDeleteResponse(list.size());
    }

    @Override
    public FieldAiMappingResponse fieldMapping(FileImportFieldMappingRequest mappingRequest) {
        FieldAiMappingResponse res = new FieldAiMappingResponse();
        Long dataTableId = mappingRequest.getDataTableId();
        Long fileId = mappingRequest.getFileId();
        dataTableManageService.validDataTableByTenantId(dataTableId);
        ImportMappingTypeEnum mappingType = mappingRequest.getMappingType();
        // 获取Excel表首行数据
        TaskFileImportCriteria criteria = new TaskFileImportCriteria();
        TaskFileImportCriteria.Criteria criteriaCriteria = criteria.createCriteria();
        criteriaCriteria.andDataTableIdEqualTo(dataTableId)
                .andIdEqualTo(fileId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<TaskFileImportWithBLOBs> tasks = taskFileImportMapper.selectByExampleWithBLOBs(criteria);
        Assert.notEmpty(tasks, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "任务不存在或已删除"));
        byte[] firstRowData = tasks.get(0).getFirstRowData();
        String json = GzipUtils.decompressToString(firstRowData);
        Assert.notBlank(json, () -> new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "获取Excel表字段失败"));
        List<RowDataDto> rowDataDtos = JsonUtils.readType(json, new TypeReference<>() {
        });
        Assert.notEmpty(rowDataDtos, () -> new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "获取Excel表字段失败"));
        // 获取数据集对客显示字段
        List<VisibleFieldResponse> visibleFields = dataTableManageService.getVisibleFields(dataTableId, Boolean.FALSE);
        Assert.notEmpty(visibleFields, () -> new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "获取数据集表字段失败"));
        // 移除oneId字段
        visibleFields = visibleFields.stream().filter(field -> !Objects.equals(field.getEnName(), Constants.TABLE_USER_ONE_ID)).collect(Collectors.toList());
        res.setExcelFields(rowDataDtos);
        res.setDorisFields(visibleFields);
        List<FieldMappingDto> mappingDtos = null;
        // 同名映射
        if (ImportMappingTypeEnum.EQUAL_NAME.equals(mappingType)) {
            mappingDtos = fieldMappingWithEqualName(rowDataDtos, visibleFields);
        } else {
            mappingDtos = fieldMappingWithAi(rowDataDtos, visibleFields);
        }
        res.setMappingRes(mappingDtos);
        return res;
    }


    @Override
    @Deprecated
    public List<UploadDetailDto> fileUpload(MultipartFile[] files, UserAuthInfo data) {
        List<UploadDetailDto> details = new ArrayList<>();
        for (MultipartFile file : files) {
            String sourceName = file.getOriginalFilename();
            String suffix = sourceName.substring(sourceName.lastIndexOf(".") + 1);
            String pured = DatetimeUtils.pureDateFormat(new Date());
            String uuid = IdUtil.randomUUID().replaceAll("-", "");
            String bosName = String.format("/%s/%s/%s.%s", bosConfig.getEnv(), pured, uuid, suffix);
            try (InputStream inputStream = file.getInputStream()) {
                bosUtils.putObject(bosConfig.getBucket().getDataSync(), bosName, inputStream);
            } catch (IOException e) {
                log.error("服务上传异常:", e);
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "服务上传异常");
            }
            details.add(new UploadDetailDto(sourceName, bosName));
        }
        return details;
    }

    /**
     * AI语义映射
     *
     * @param excelFields Excel首行数据
     * @param dorisFields 数据集可见字段
     * @return
     */
    public List<FieldMappingDto> fieldMappingWithAi(List<RowDataDto> excelFields, List<VisibleFieldResponse> dorisFields) {
        // 构建AI大模型请求参数
        Map<String, List<String>> fields = new HashMap<>();
        List<String> excelFiledList = new ArrayList<>();
        List<String> dorisFiledList = new ArrayList<>();
        String template = "%s-%s-%s";
        for (RowDataDto row : excelFields) {
            excelFiledList.add(String.format(template, row.getEnName(), row.getCnName(), row.getDataType()));
        }
        for (VisibleFieldResponse field : dorisFields) {
            dorisFiledList.add(String.format(template, field.getEnName(), field.getCnName(), field.getDataType()));
        }
        fields.put("Excel字段信息", excelFiledList);
        fields.put("数据集字段信息", dorisFiledList);
        String reqStr = JSONUtil.toJsonStr(fields);
        List<FieldMappingDto> aiMapping = aiBaseService.fieldMapping(reqStr);
        if (CollUtil.isEmpty(aiMapping)) {
            aiMapping = Collections.emptyList();
        }
        // 完全映射，直接返回
        if (aiMapping.size() == dorisFields.size()) {
            return aiMapping;
        }
        // 补全未映射上的字段
        Map<String, FieldMappingDto> map = new HashMap<>();
        for (FieldMappingDto dto : aiMapping) {
            map.put(dto.getTagEnName(), dto);
        }
        List<FieldMappingDto> list = new ArrayList<>();
        for (VisibleFieldResponse field : dorisFields) {
            FieldMappingDto dto = map.get(field.getEnName());
            list.add(dto != null ? dto : new FieldMappingDto(field));
        }
        return list;
    }

    /**
     * 字段同名映射
     *
     * @param rowDataDtos   Excel首行数据
     * @param visibleFields 数据集可见字段
     * @return
     */
    public List<FieldMappingDto> fieldMappingWithEqualName(List<RowDataDto> rowDataDtos, List<VisibleFieldResponse> visibleFields) {
        List<FieldMappingDto> mappingDtos = new ArrayList<>();
        Map<String, RowDataDto> enMap = new HashMap<>();
        Map<String, RowDataDto> cnMap = new HashMap<>();
        for (RowDataDto dto : rowDataDtos) {
            enMap.put(dto.getEnName(), dto);
            cnMap.put(dto.getCnName(), dto);
        }
        for (VisibleFieldResponse field : visibleFields) {
            String enName = field.getEnName();
            String cnName = field.getCnName();
            RowDataDto excelField = enMap.get(enName);
            if (excelField == null) {
                excelField = cnMap.get(cnName);
            }
            mappingDtos.add(new FieldMappingDto(excelField, field));
        }
        return mappingDtos;
    }

    /**
     * 导入文件中的数据
     *
     * @param taskFileImport
     * @param fileImportRequest
     * @param res
     * @param tableInfo
     */
    @Override
    public List<Map<String, Object>> importFileData(TaskFileImportWithBLOBs taskFileImport,
                                                    FileImportRequest fileImportRequest,
                                                    FileImportTaskResponse res,
                                                    DataTableInfo tableInfo) {
        List<Map<String, Object>> errors = null;
        try {
            String groupId = taskFileImport.getGroupId();
            log.info("dataImport: groupId:{} begin import", groupId);
            String suffixName = taskFileImport.getSuffixName();
            if ("csv".equals(suffixName)) {
                errors = importByCsv(taskFileImport, fileImportRequest, res, tableInfo);
            } else {
                errors = importByXlsOrXlsx(taskFileImport, fileImportRequest, res, tableInfo);
            }
            taskFileImport.setStatus(ImportStatusEnum.IMPORT_SUCCESS.getStatus());
        } catch (Exception e) {
            taskFileImport.setMessage("数据导入异常:" + e.getMessage());
            taskFileImport.setStatus(ImportStatusEnum.IMPORT_FAIL.getStatus());
            res.setMessage("文件数据接入异常：" + taskFileImport.getSourceName());
            log.error("taskId:{} 数据导入异常", taskFileImport.getId(), e);
        }
        // 刷新导入情况
        taskFileImportMapper.updateByPrimaryKeyWithBLOBs(taskFileImport);
        return errors;
    }

    /**
     * 导入xls\xlsx格式文件数据
     *
     * @param taskFileImport
     * @param fileImportRequest
     * @param res
     * @param tableInfo
     */
    public List<Map<String, Object>> importByXlsOrXlsx(TaskFileImport taskFileImport,
                                                       FileImportRequest fileImportRequest,
                                                       FileImportTaskResponse res,
                                                       DataTableInfo tableInfo) {
        DbTypeEnum dbEnum = DbTypeEnum.getDbEnum(tableInfo.getDbType());
        List<Map<String, Object>> errors = new ArrayList<>();
        String dataTableId = taskFileImport.getDataTableId().toString();
        List<FieldMappingDto> fieldMappings = fileImportRequest.getFieldMappings();
        BosObject bosObject = bosUtils.getObject(bosConfig.getBucket().getDataSync(), taskFileImport.getBosName());
        if (bosObject == null) {
            taskFileImport.setMessage("文件不存在");
            taskFileImport.setStatus(ImportStatusEnum.IMPORT_FAIL.getStatus());
            res.setMessage(taskFileImport.getMessage());
            return errors;
        }
        CompletionService<Void> completionService = new ExecutorCompletionService<>(executorService);
        AtomicInteger atomicCount = new AtomicInteger(0);
        InputStream inputStream = bosObject.getObjectContent();
        // 自定义行处理逻辑
        ExcelUtil.readBySax(inputStream, 0, new RowHandler() {
            private final List<String> sourceNames = new ArrayList<>();
            private int count = 0;

            @Override
            public void handle(int i, long l, List<Object> list) {
                count++;
                // 如果是第一行，获取英文字段名
                if (count == 1) {
                    list.forEach(name -> sourceNames.add(name.toString().replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY)));
                }
                // 业务数据行且长度符合
                int size = list.size();
                if (count >= 3) {
                    Map<String, Object> excelData = new HashMap<>();
                    for (int i1 = 0; i1 < sourceNames.size(); i1++) {
                        Object dataObj = size >= (i1 + 1) ? list.get(i1) : null;
                        excelData.put(sourceNames.get(i1), dataObj);
                    }
                    Map<String, Object> dorisData = excFieldMapping(excelData, fieldMappings, dbEnum);
                    completionService.submit(() -> saveData(excelData, errors, res, taskFileImport, dataTableId, dorisData));
                    atomicCount.incrementAndGet();
                }
            }
        });
        // 等待线程执行完成，主要是获取成功失败条数
        for (int i = 0; i < atomicCount.get(); i++) {
            try {
                // 避免计数异常导致阻塞
                completionService.poll(3, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("导入{}线程执行异常", taskFileImport.getSourceName(), e);
            }
        }
        log.info("{} 文件导入结束", taskFileImport.getSourceName());
        return errors;
    }

    /**
     * 导入csv文件格式数据
     *
     * @param taskFileImport
     * @param fileImportRequest
     * @param response
     * @param tableInfo
     */
    public List<Map<String, Object>> importByCsv(TaskFileImport taskFileImport,
                                                 FileImportRequest fileImportRequest,
                                                 FileImportTaskResponse response,
                                                 DataTableInfo tableInfo) {
        DbTypeEnum dbEnum = DbTypeEnum.getDbEnum(tableInfo.getDbType());
        Long id = taskFileImport.getDataTableId();
        List<FieldMappingDto> fieldMappings = fileImportRequest.getFieldMappings();
        String dataTableId = id.toString();
        List<Map<String, Object>> errors = new ArrayList<>();
        BosObject bosObject = bosUtils.getObject(bosConfig.getBucket().getDataSync(), taskFileImport.getBosName());
        if (bosObject == null) {
            taskFileImport.setMessage("文件不存在");
            taskFileImport.setStatus(ImportStatusEnum.IMPORT_FAIL.getStatus());
            response.setMessage(taskFileImport.getMessage());
            return errors;
        }
        CompletionService<Void> completionService = new ExecutorCompletionService<>(executorService);
        AtomicInteger atomicCount = new AtomicInteger(0);
        try (InputStream inputStream = bosObject.getObjectContent();
             InputStreamReader reader = new InputStreamReader(inputStream)) {
            CsvReader csvReader = CsvUtil.getReader(reader);
            AtomicInteger count = new AtomicInteger(0);
            List<String> enNames = new ArrayList<>();
            csvReader.read(csvRow -> {
                int andGet = count.incrementAndGet();
                if (andGet == 1) {
                    csvRow.getRawList().forEach(name -> enNames.add(name.replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY)));
                }
                if (andGet >= 3) {
                    List<String> rawList = csvRow.getRawList();
                    int size = rawList.size();
                    Map<String, Object> itemMap = new HashMap<>();
                    for (int i = 0; i < enNames.size(); i++) {
                        String dataStr = size >= (i + 1) ? rawList.get(i) : null;
                        itemMap.put(enNames.get(i), dataStr);
                    }
                    // 映射字段，做必要值类型转换
                    Map<String, Object> dataMap = excFieldMapping(itemMap, fieldMappings, dbEnum);
                    // 异步校验数据，下发kafka，统计失败&成功数量
                    completionService.submit(() -> saveData(itemMap, errors, response, taskFileImport, dataTableId, dataMap));
                    atomicCount.incrementAndGet();
                }
            });
        } catch (IOException e) {
            taskFileImport.setMessage("导入数据异常");
            taskFileImport.setStatus(ImportStatusEnum.IMPORT_FAIL.getStatus());
            log.error("dataImport: IOException, taskId:{},", taskFileImport.getId(), e);
        }
        // 等待线程执行完成，主要是获取成功失败条数
        for (int i = 0; i < atomicCount.get(); i++) {
            try {
                // 避免计数异常导致阻塞
                completionService.poll(3, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("导入{}线程执行异常", taskFileImport.getSourceName(), e);
            }
        }
        log.info("{} 文件导入结束", taskFileImport.getSourceName());
        return errors;
    }

    /**
     * 存储数据
     * 捕获异常，记录成功、失败的数据
     *
     * @param excelData      excel表数据
     * @param errors         错误集合
     * @param response       响应
     * @param taskFileImport 任务记录
     * @param dataTableId    doris 表ID
     * @param dorisData      doris映射数据
     */
    public Void saveData(Map<String, Object> excelData,
                         List<Map<String, Object>> errors,
                         FileImportTaskResponse response,
                         TaskFileImport taskFileImport,
                         String dataTableId,
                         Map<String, Object> dorisData) {
        try {
            this.handleSingleSync(dorisData, dataTableId);
            response.getSuccessCount().incrementAndGet();
        } catch (Exception e) {
            if (e instanceof DeepSightException.ParamsErrorException paramsErrorException) {
                excelData.put("deeepSightErrorMsg", paramsErrorException.getMessage());
            }
            taskFileImport.setStatus(ImportStatusEnum.IMPORT_FAIL.getStatus());
            errors.add(excelData);
            response.getFailCount().incrementAndGet();
        }
        return null;
    }

    /**
     * 字段映射:doris\es值类型转换
     *
     * @param itemMap       文件读取数据
     * @param fieldMappings 字段映射列表
     * @param dbEnum        数据库枚举
     * @return
     */
    public Map<String, Object> excFieldMapping(Map<String, Object> itemMap, List<FieldMappingDto> fieldMappings, DbTypeEnum dbEnum) {
        HashMap<String, Object> map = new HashMap<>();
        for (FieldMappingDto mapping : fieldMappings) {
            String sourceEnName = mapping.getSourceEnName();
            String tagEnName = mapping.getTagEnName();
            Object data = null;
            if (StrUtil.isNotBlank(sourceEnName)) {
                data = itemMap.get(sourceEnName);
            }
            if (Objects.equals(DbTypeEnum.DORIS_TYPE, dbEnum)) {
                map.put(tagEnName, dorisService.covertDorisValue(mapping.getTagType(), data));
            } else if (Objects.equals(DbTypeEnum.ES_TYPE, dbEnum)) {
                map.put(tagEnName, covertEsValue(mapping.getTagType(), data));
            }
        }
        return map;
    }

    public Object covertEsValue(String dataType, Object data) {
        ESToJavaTypeMapping mapping = ESToJavaTypeMapping.getJavaType(dataType);
        if (data == null || mapping == null) {
            return null;
        }
        String type = mapping.getType();
        switch (type) {
            // 数字、时间类型
            case Constants.CONSTANT_NUMBER:
                if (Objects.equals("date", dataType)) {
                    try {
                        DateTime parse = DateUtil.parse(data.toString());
                        data = new Date(parse.getTime());
                    } catch (Exception e) {
                        log.error("es date parser error:{},msg:{}", data, e.getMessage());
                    }
                } else if (!(data instanceof Number)) {
                    try {
                        data = parserNumber(dataType, data);
                    } catch (Exception e) {
                        log.error("es parser number Exception:{},msg:{}", data, e.getMessage());
                    }
                }
                break;
            case Constants.CONSTANT_STRING:
                if (!(data instanceof String)) {
                    data = data.toString();
                }
                break;
            case Constants.CONSTANT_BOOLEAN:
                if (!(data instanceof Boolean)) {
                    try {
                        data = Boolean.parseBoolean(data.toString());
                    } catch (Exception e) {
                        log.error("es parser boolean Exception:{}, msg:{}", data, e.getMessage());
                    }
                }
                break;
            case Constants.CONSTANT_LIST_STRING:
                try {
                    data = JsonUtils.readType(data.toString(), new TypeReference<List<String>>() {
                    });
                } catch (Exception e) {
                    log.error("ES parser list Exception:{}", data, e);
                }
                break;
            default:
                break;
        }
        return data;
    }

    /**
     * 解析数字
     *
     * @param dataType
     * @param data
     * @return
     */
    public Object parserNumber(String dataType, Object data) {
        String dataString = data.toString();
        if (Objects.equals("float", dataType) || dataString.contains(".")) {
            return Float.parseFloat(dataString);
        }
        return Long.parseLong(dataString);
    }

    /**
     * 获取一行数据
     * 解析数据文件，分别获取前三行数据，目前只支持Excel、xls、xlsx、csv
     * 第一行：字段英文名
     * 第二行：字段中文名
     * 第三行：业务数据
     *
     * @param file 文件详情
     * @return 文件中的一行数据
     */
    public List<RowDataDto> getOneDataFromFile(FileDetailDto file) {
        String originalFilename = file.getFileName();
        String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        List<String> enNames = new ArrayList<>();
        List<String> cnNames = new ArrayList<>();
        List<Object> rowDatas = new ArrayList<>();
        String dataSync = bosConfig.getBucket().getDataSync();
        try (BosObject bosObject = bosUtils.getObject(dataSync, file.getBosKey())) {
            Assert.notNull(bosObject, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "文件不存在"));
            if ("csv".equals(fileSuffixName)) {
                try (InputStream inputStream = bosObject.getObjectContent();
                     InputStreamReader reader = new InputStreamReader(inputStream)) {
                    CsvReader csvReader = CsvUtil.getReader(reader);
                    AtomicInteger count = new AtomicInteger();
                    csvReader.read(csvRow -> {
                        int andGet = count.incrementAndGet();
                        if (andGet > 3) {
                            return;
                        }
                        if (andGet == 1) {
                            csvRow.getRawList().forEach(name -> enNames.add(name.replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY)));
                        } else if (andGet == 2) {
                            cnNames.addAll(csvRow.getRawList());
                        } else if (andGet == 3) {
                            rowDatas.addAll(csvRow.getRawList());
                        }
                    });
                }
            } else {
                try (InputStream inputStream = bosObject.getObjectContent();
                     ExcelReader reader = ExcelUtil.getReader(inputStream, 0)) {
                    List<List<Object>> read = reader.read(0, 2);
                    Assert.isTrue(read.size() == 3, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "数据行数错误，需大于等于3行"));
                    read.get(0).forEach(name -> {
                        String enName = name.toString();
                        if (StrUtil.isNotBlank(enName)) {
                            enNames.add(enName);
                        }
                    });
                    read.get(1).forEach(name -> {
                        String cnName = name.toString();
                        if (StrUtil.isNotBlank(cnName)) {
                            cnNames.add(cnName);
                        }
                    });
                    rowDatas.addAll(read.get(2));
                }
            }
            if (CollUtil.isEmpty(enNames) || CollUtil.isEmpty(cnNames) || CollUtil.isEmpty(rowDatas)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "数据行数错误，需大于等于3行");
            }
            if (enNames.size() != cnNames.size() || rowDatas.size() == 0) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "前三行数据长度不一致");
            }
            // 首行英文字段校验
            for (String enName : enNames) {
                enName = enName.replaceAll("[\\s\\n\\r\\t]", StrUtil.EMPTY);
                Assert.isTrue(Pattern.matches(enFieldPattern, enName), () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "首行数据必须为英文字段名"));
            }
            List<RowDataDto> rowData = new ArrayList<>();
            int length = rowDatas.size();
            Set<String> names = new HashSet<>();
            for (int i = 0; i < enNames.size(); i++) {
                Object dataStr = length >= (i + 1) ? rowDatas.get(i) : null;
                // 转换时间固定格式
                if (dataStr instanceof Date) {
                    dataStr = DatetimeUtils.formatDate((Date) dataStr);
                }
                // Excel字段名去重
                String enName = enNames.get(i);
                if (!names.contains(enName)) {
                    names.add(enName);
                    rowData.add(new RowDataDto(enName, cnNames.get(i), dataStr));
                }
            }
            return rowData;
        } catch (POIException e) {
            log.error("读取导入数据集文件首行数据POIException异常:", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "文件为空或文件损坏");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("读取导入数据集文件首行数据异常:", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "读取文件样例数据异常");
        }
    }
}
