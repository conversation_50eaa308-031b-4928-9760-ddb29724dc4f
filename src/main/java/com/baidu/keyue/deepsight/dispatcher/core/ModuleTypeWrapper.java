package com.baidu.keyue.deepsight.dispatcher.core;

import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;


@Component
public class ModuleTypeWrapper {

    @Autowired
    private Environment environment;

    public int getRetryLimit(ModuleType moduleType) {
        return environment.getProperty(String.format("%s.retry.limit", lowerOfName(moduleType.name())), int.class,
                moduleType.getRetryLimit());
    }

    public int getParallelSize(ModuleType moduleType) {
        return environment.getProperty(String.format("%s.parallel.size", lowerOfName(moduleType.name())), int.class,
                moduleType.getParallelSize());
    }

    public int getScanInMilli(ModuleType moduleType) {
        return environment.getProperty(String.format("%s.scan.in.milli", lowerOfName(moduleType.name())), int.class,
                moduleType.getScanInMilli());
    }

    public int getRetryWaitSecond(ModuleType moduleType) {
        return environment.getProperty(String.format("%s.retry.wait.second", lowerOfName(moduleType.name())), int.class,
                moduleType.getRetryWaitSecond());
    }

    public boolean isSerial(ModuleType moduleType) {
        return environment.getProperty(String.format("%s.serial", lowerOfName(moduleType.name())), boolean.class,
                moduleType.isSerial());
    }

    private String lowerOfName(String name) {
        return name.replace("_", ".").toLowerCase();
    }
}
