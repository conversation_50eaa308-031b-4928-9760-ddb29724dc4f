package com.baidu.keyue.deepsight.dispatcher.annotation;


import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface TaskExecutor {
    ModuleType[] value();
}
