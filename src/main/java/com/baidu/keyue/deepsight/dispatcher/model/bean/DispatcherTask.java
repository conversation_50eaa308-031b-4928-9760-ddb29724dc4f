package com.baidu.keyue.deepsight.dispatcher.model.bean;

import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Created by IDEA
 *
 * <AUTHOR>
 * created at 2020/4/7 4:36 下午
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DispatcherTask {
    private String id;

    private String botId;

    private String agentId;

    private String tenantId;

    private String createUserId;

    private String createUserName;

    private Map<String, Object> params;

    @NonNull
    private ModuleType moduleType;

    private String ruleId;

    private String serialKey;

    public static DispatcherTask convertFromBean(DispatcherTaskBean taskBean) {
        return DispatcherTask.builder()
                .id(taskBean.getId())
                .botId(taskBean.getBotId())
                .agentId(taskBean.getAgentId())
                .tenantId(taskBean.getTenantId())
                .createUserId(taskBean.getCreateUserId())
                .createUserName(taskBean.getCreateUserName())
                .params(JsonUtils.toMapWithoutException(taskBean.getParams()))
                .moduleType(taskBean.getModule())
                .ruleId(taskBean.getRuleId())
                .serialKey(taskBean.getSerialKey())
                .build();
    }

    /**
     * 当前任务是否是规则自动创建的
     *
     * @return boolean
     */
    public boolean isGenerateByRule() {
        return StringUtils.isNotEmpty(ruleId);
    }
}
