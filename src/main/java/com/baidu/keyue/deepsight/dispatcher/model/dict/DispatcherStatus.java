package com.baidu.keyue.deepsight.dispatcher.model.dict;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

public enum DispatcherStatus {
    CREATE("create", "新建"),
    RUNNING("running", "运行"),
    READY("ready", "已就位"),
    ABORT("abort", "已取消"),
    INTERRUPTING("interrupt", "取消中"),
    FAIL("fail", "失败"),
    //    EFFECT("effect", "生效中"),
    SUCCESS("success", "成功");

    private static final Map<String, DispatcherStatus> codeMapIndex = new HashMap<>();

    static {
        for (DispatcherStatus item : EnumSet.allOf(DispatcherStatus.class)) {
            codeMapIndex.put(item.getStatus(), item);
        }
    }

    private String code;
    private String desc;

    DispatcherStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DispatcherStatus valueOfStatus(String code) {
        return codeMapIndex.get(code);
    }

    public String getStatus() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
