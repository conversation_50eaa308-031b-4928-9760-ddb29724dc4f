package com.baidu.keyue.deepsight.dispatcher.model.dto;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class MockDataDTO {

    private String tenantId;

    /**
     * mock_user_tenantId
     */
    private String tableName;


    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}
