package com.baidu.keyue.deepsight.dispatcher.core;

import com.baidu.keyue.deepsight.dispatcher.model.dict.DispatcherConstants;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherHandlerService;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 调度管理启动器
 * 负责任务发现及分发、任务超时检测等
 *
 * <AUTHOR>
 * created at 2020/4/8 11:00 上午
 */
@Slf4j
@Component
@EnableScheduling
public class DispatcherRunner implements ApplicationRunner {

    private final DispatcherTaskManager taskManager;
    private final DispatcherHandlerService dispatcherHandlerService;

    @Autowired
    private ModuleTypeWrapper moduleTypeWrapper;

    @Value("${dispatcher.expire.second:5}")
    private int defaultExpire;

    @Value("${dispatcher.enable:true}")
    private boolean dispatcherEnable;

    /**
     * 是否为升级场景
     */
    @Value("${upgrade.switch:true}")
    private boolean upgradeSwitch;

    public DispatcherRunner(DispatcherTaskManager taskManager,
                            DispatcherHandlerService dispatcherHandlerService) {
        this.taskManager = taskManager;
        this.dispatcherHandlerService = dispatcherHandlerService;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (upgradeSwitch) {
            log.info("dispatcher runner finish, env is upgrade.");
            return;
        }
        if (!dispatcherEnable) {
            log.info("dispatcher runner finish, dispatcher.enable is false.");
            return;
        }

        log.info("dispatcher runner start.");
        taskManager.init();
        // 任务发现
        EnumSet<ModuleType> modules = EnumSet.allOf(ModuleType.class);
        // 移除测试需要的module
        modules.remove(ModuleType.MOCK_TEST);
        for (ModuleType module : modules) {
            ScheduledExecutorService taskExecutor =
                    Executors.newSingleThreadScheduledExecutor(
                            new ScheduleThreadFactory(
                                    module.name() + DispatcherConstants.RUNNER_EXECUTOR_FACTORY_NAME));
            // 按模块执行任务发现
            taskExecutor.scheduleWithFixedDelay(
                    new DispatcherStarter(module),
                    0,
                    moduleTypeWrapper.getScanInMilli(module),
                    TimeUnit.MILLISECONDS);
            log.info("dispatcher runner start module: [{}] success.", module.name());
        }

        // 超时检测定时器
        int expireSecond = defaultExpire * 5;
        ScheduledExecutorService expiredCheckerExecutor =
                Executors.newSingleThreadScheduledExecutor(
                        new ScheduleThreadFactory(DispatcherConstants.CHECKER_EXECUTOR_FACTORY_NAME));
        expiredCheckerExecutor.scheduleWithFixedDelay(
                () -> {
                    try {
                        log.info("start to check expire task.");
                        // 检查是否有处于RUNNING状态的任务超过了5倍过期时间还没有被更新
                        taskManager.checkExpire(expireSecond);
                    } catch (Exception e) {
                        log.error("check expire task error, expireSecond is {}s, caused by ", expireSecond, e);
                    }
                }, 0, expireSecond, TimeUnit.SECONDS);
        log.info("dispatcher runner expired task checker start success.");
    }


    class DispatcherStarter implements Runnable {

        private final ModuleType module;

        public DispatcherStarter(ModuleType module) {
            this.module = module;
        }

        @Override
        public void run() {
            try {
                log.debug("Module: [{}] start", module);
                // 执行任务超过并发限制，这里有可能出现超过最大并发度的情况，但多提交的任务会被放到阻塞队列。
                if (taskManager.runningTaskCount(module) >= moduleTypeWrapper.getParallelSize(module)) {
                    log.error("dispatcher runner 超过并发限制, size={}", taskManager.runningTaskCount(module));
                    return;
                }
                DispatcherTaskBean taskBean;
                boolean serial = moduleTypeWrapper.isSerial(module);
                if (serial) {
                    // 如果是串行的模块，则查询串行agent的任务
                    taskBean = dispatcherHandlerService.getOldestTaskForSerialAgent(module);
                } else {
                    // 并行的任务，查询所有可用的任务
                    taskBean = dispatcherHandlerService.getOldestTask(module);
                }
                if (Objects.isNull(taskBean)) {
                    return;
                }
                log.debug("Module: [{}], query task: [{}], agent id is: [{}], is serial: [{}]", module.name(),
                        taskBean.getId(), taskBean.getAgentId(), serial);
                // 修改CREATE状态的任务为RUNNING，修改成功则竞争到该任务
                boolean success = dispatcherHandlerService.tryAcquireTask(taskBean.getId());
                if (!success) {
                    return;
                }
                log.debug("Module: [{}], compete task: [{}] success.", module.name(), taskBean.getId());
                // 提交任务到各模块对应的线程池中进行处理
                taskManager.submit(taskBean);
            } catch (Exception e) {
                log.error("run dispatcher error: ", e);
            }
        }
    }
}
