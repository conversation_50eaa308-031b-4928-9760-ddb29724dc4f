package com.baidu.keyue.deepsight.dispatcher.model.dict;

/**
 * Created by IDEA
 *
 * <AUTHOR>
 * created at 2020/4/23 6:08 下午
 */
public class DispatcherConstants {
    public static final String CORE_EXECUTOR_FACTORY_NAME = "-dispatcher-manager";
    public static final String RUNNER_EXECUTOR_FACTORY_NAME = "-dispatcher-runner";
    public static final String CHECKER_EXECUTOR_FACTORY_NAME = "dispatcher-expire-checker";
    public static final String TICKER_THREAD_NAME = "-ticker";
    public static final String SUCCESS = "success";
    public static final String FAIL = "fail";

    /**
     * 刷新任务规则的锁名称
     */
    public static final String REFRESH_LOCK_KEY = "dispatcher_refresh_by_module_%s";

    /**
     * 刷新任务规则产生的任务锁失效时间，默认三十分钟
     */
    public static final int REFRESH_LOCK_IN_SECOND = 30 * 60;

    public static final String CLEAR_REFRESH_LOCK_KEY = "clear_version_record_lock";

}
