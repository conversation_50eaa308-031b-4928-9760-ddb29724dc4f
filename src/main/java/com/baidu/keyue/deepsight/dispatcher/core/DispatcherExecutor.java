package com.baidu.keyue.deepsight.dispatcher.core;


import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;

/**
 * 各模块任务实现及回调接口
 * 命名注意：该接口的实现类是通过Spring的bean工厂获取的，获取到后找到TaskExecutor注解，并读取Module
 * 如果该接口的实现类使用Service或ServiceImpl命名结尾会与Spring的事物管理命名冲突，从而使用Cglib代理生成示例，会获取不到接口
 * Spring事物配置参考：com.baidu.icpd.common.base.mysql.DefaultMybatisConfiguration#txProxy
 */
public interface DispatcherExecutor {

    void execute(DispatcherTask task) throws Exception;

    default void success(DispatcherTask task) {

    }

    default void fail(DispatcherTask task) {

    }

    default void complete(DispatcherTask task) {

    }

    default void cancel(DispatcherTask task) {

    }
}
