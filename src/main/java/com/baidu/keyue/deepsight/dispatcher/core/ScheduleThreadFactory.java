package com.baidu.keyue.deepsight.dispatcher.core;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;


public class ScheduleThreadFactory implements ThreadFactory {

    private static final String THREAD_FACTORY_NAME = "%s%d-thread-";
    private static final AtomicInteger POOL_NUMBER = new AtomicInteger(0);

    private final AtomicInteger threadNumber = new AtomicInteger(0);
    private final String namePrefix;

    public ScheduleThreadFactory(String poolPrefix) {
        namePrefix = String.format(THREAD_FACTORY_NAME, poolPrefix, POOL_NUMBER.getAndIncrement());
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
        thread.setDaemon(false);
        return thread;
    }
}
