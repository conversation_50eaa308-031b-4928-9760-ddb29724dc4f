package com.baidu.keyue.deepsight.dispatcher.core;


import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;

import java.util.Date;
import java.util.List;

public interface DispatcherManager {
    /**
     * 提交任务，执行时间默认与提交时间相同。若存在id则使用已存在的id，否则重新初始化uuid。注意：使用自己的id需要保证id不重复
     *
     * @param task 待提交的任务
     * @return 返回任务id
     */
    String submit(DispatcherTask task);

    /**
     * 指定执行时间的任务。若存在id则使用已存在的id，否则重新初始化uuid。注意：使用自己的id需要保证id不重复
     *
     * @param task        待提交的任务
     * @param executeTime 任务执行时间
     * @return 返回任务id
     */
    String submit(DispatcherTask task, Date executeTime);

    void cancel(String taskId, String agentId, ModuleType module);

    /**
     * 指定模块是否存在正在执行的任务（任务状态是否有RUNNING的）
     *
     * @param module 任务模块
     * @return 是否存在执行的任务
     */
    boolean isRunning(ModuleType module);

    /**
     * 指定agentId的模块是否存在正在执行的任务（任务状态是否有RUNNING的）
     *
     * @param module  任务模块
     * @param agentId agentId
     * @return 是否存在执行的任务
     */
    boolean isRunning(ModuleType module, String agentId);

    /**
     * 指定agentId的模块是否存在正在执行的任务（任务状态是否有RUNNING的）
     *
     * @param module  任务模块
     * @param agentId agentId
     * @return 是否存在执行的任务
     */
    boolean isRunningAndCreateByAgentId(ModuleType module, String agentId);


    void retry(String id, String agentId, ModuleType moduleType);

    /**
     * 批量提交任务
     *
     * @param taskList
     */
    void batchSubmit(List<DispatcherTask> taskList);
}
