package com.baidu.keyue.deepsight.dispatcher.service;


import com.baidu.keyue.deepsight.dispatcher.model.dict.DispatcherStatus;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean;

import java.util.Date;
import java.util.List;

public interface DispatcherHandlerService {
    String create(DispatcherTask task, Date executeTime);

    DispatcherTaskBean findTaskById(String id, String agentId, ModuleType module);

    void update(DispatcherTaskBean taskBean);

    boolean tryAcquireTask(String taskId);

    DispatcherTaskBean getOldestTask(ModuleType module);

    DispatcherTaskBean getOldestTaskForSerialAgent(ModuleType module);

    List<DispatcherTaskBean> checkExpire(int expire);

    void refreshTask(String taskId, String agentId, ModuleType module);

    void success(String id, String agentId, ModuleType module);

    void fail(String id, String agentId, ModuleType module);

    void retry(String id, String agentId, ModuleType moduleType);

    void cancel(String id, String agentId, ModuleType module);

    boolean isRunningByAgentId(ModuleType module, String agentId);

    boolean isRunningAndCreateByAgentId(ModuleType module, String agentId);

    void deleteCreateTaskByAgentId(String agentId);

    /**
     * 查询一个agent下的特定模块下，若干运行状态的任务
     *
     * @param agentId
     * @param module
     * @param statusList
     * @return
     */
    List<DispatcherTaskBean> findByAgentIdAndModuleWithStatus(String agentId, ModuleType module,
                                                              List<DispatcherStatus> statusList);

    /**
     * 批量创建任务
     *
     * @param taskList
     * @param executeTime
     */
    void batchCreate(List<DispatcherTask> taskList, Date executeTime);

}
