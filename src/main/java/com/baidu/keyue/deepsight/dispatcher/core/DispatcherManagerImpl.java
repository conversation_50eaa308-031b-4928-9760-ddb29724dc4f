package com.baidu.keyue.deepsight.dispatcher.core;

import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherHandlerService;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherTaskManager;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * Created by IDEA
 *
 * <AUTHOR>
 * created at 2020/4/7 4:38 下午
 */
@Service
public class DispatcherManagerImpl implements DispatcherManager {

    private final DispatcherHandlerService dispatcherHandlerService;
    private final DispatcherTaskManager taskManager;


    public DispatcherManagerImpl(
            DispatcherHandlerService dispatcherHandlerService,
            DispatcherTaskManager taskManager) {
        this.dispatcherHandlerService = dispatcherHandlerService;
        this.taskManager = taskManager;
    }

    @Override
    public String submit(DispatcherTask task) {
        return submit(task, new Date());
    }

    @Override
    public String submit(DispatcherTask task, Date executeTime) {
        return dispatcherHandlerService.create(task, executeTime);
    }

    @Override
    public void cancel(String taskId, String agentId, ModuleType module) {
        taskManager
                .cancel(DispatcherTask.convertFromBean(dispatcherHandlerService.findTaskById(taskId, agentId, module)));
    }

    @Override
    public boolean isRunning(ModuleType module, String agentId) {
        return dispatcherHandlerService.isRunningByAgentId(module, agentId);
    }

    @Override
    public boolean isRunning(ModuleType module) {
        return isRunning(module, null);
    }

    /**
     * 指定agentId的模块是否存在正在执行的任务（任务状态是否有RUNNING的）
     *
     * @param module  任务模块
     * @param agentId agentId
     * @return 是否存在执行的任务
     */
    @Override
    public boolean isRunningAndCreateByAgentId(ModuleType module, String agentId) {
        return dispatcherHandlerService.isRunningAndCreateByAgentId(module, agentId);
    }


    @Override
    public void retry(String id, String agentId, ModuleType moduleType) {
        dispatcherHandlerService.retry(id, agentId, moduleType);
    }

    @Override
    public void batchSubmit(List<DispatcherTask> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        dispatcherHandlerService.batchCreate(taskList, new Date());
    }
}
