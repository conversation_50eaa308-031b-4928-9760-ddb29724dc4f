package com.baidu.keyue.deepsight.dispatcher.service;


import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;

public interface DispatcherTaskManager {

    void init();

    void submit(DispatcherTaskBean taskBean);

    void checkExpire(int expire);

    int runningTaskCount(ModuleType module);

    void cancel(DispatcherTask task);

    void success(DispatcherTask task);

    void fail(DispatcherTask task);
}
