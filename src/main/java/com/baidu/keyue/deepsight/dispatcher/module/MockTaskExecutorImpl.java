package com.baidu.keyue.deepsight.dispatcher.module;

import com.baidu.keyue.deepsight.dispatcher.annotation.TaskExecutor;
import com.baidu.keyue.deepsight.dispatcher.core.DispatcherExecutor;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;
import com.baidu.keyue.deepsight.dispatcher.model.dto.MockDataDTO;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Desc: 模型训练任务调度业务方法
 */
@Slf4j
@Service
@TaskExecutor(ModuleType.MOCK_TEST)
public class MockTaskExecutorImpl implements DispatcherExecutor {

    @Override
    public void execute(DispatcherTask task) throws Exception {
        MockDataDTO dto = JsonUtils.mapToObj(task.getParams(), MockDataDTO.class);
        String tableName = dto.getTableName();
        log.info("[start]execute mockTask, taskId: {}, handle table: {}", task.getId(), tableName);
        // TODO 具体业务逻辑
        log.info("[finish]execute mockTask, taskId:{}, table: {}", task.getId(), tableName);
    }

    @Override
    public void success(DispatcherTask task) {
        log.info("[success]mockTask: taskId:{}, params:{}",
                task.getId(), JsonUtils.toJsonWithOutException(task.getParams()));
    }

    @Override
    public void fail(DispatcherTask task) {
        log.error("[fail]mockTask: taskId:{}, params:{}",
                task.getId(), JsonUtils.toJsonWithOutException(task.getParams()));
    }

}
