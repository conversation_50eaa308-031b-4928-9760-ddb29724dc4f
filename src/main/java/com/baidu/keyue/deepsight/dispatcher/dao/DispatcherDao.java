package com.baidu.keyue.deepsight.dispatcher.dao;

import com.baidu.keyue.deepsight.dispatcher.model.dict.DispatcherStatus;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface DispatcherDao {
    void insert(DispatcherTaskBean taskBean);

    void update(DispatcherTaskBean taskBean);

    DispatcherTaskBean findOlderTaskByStatus(@Param("status") DispatcherStatus status,
                                             @Param("module") ModuleType module);

    /**
     * 查询一个最近可以执行的任务，该任务所在agent当前在该模块没有正在执行的任务
     *
     * @param module
     * @return
     */
    DispatcherTaskBean findOlderTaskByStatusAndUniqueSerialKey(@Param("module") ModuleType module);

    DispatcherTaskBean findById(@Param("id") String id, @Param("agentId") String agentId,
                                @Param("module") ModuleType module);

    List<DispatcherTaskBean> findByAgentId(@Param("agentId") String agentId);

    List<DispatcherTaskBean> findExpiredTask(@Param("expire") Date expire, @Param("status") DispatcherStatus status);

    /**
     * 查询一个agent下的特定模块下，若干运行状态的任务
     *
     * @param agentId
     * @param module
     * @param statusList
     * @return
     */
    List<DispatcherTaskBean> findByAgentIdAndModuleWithStatus(@Param("agentId") String agentId,
                                                              @Param("module") ModuleType module,
                                                              @Param("list") List<DispatcherStatus> statusList);

    int tryAcquireTask(@Param("id") String taskId);

    int countTaskByStatusAndAgentId(@Param("module") ModuleType module, @Param("agentId") String agentId,
                                    @Param("status") DispatcherStatus status);

    void deleteCreateTaskByAgentId(@Param("agentId") String agentId);

    /**
     * 批量插入任务
     *
     * @param taskBeanList
     */
    void batchInsert(@Param("list") List<DispatcherTaskBean> taskBeanList);
}
