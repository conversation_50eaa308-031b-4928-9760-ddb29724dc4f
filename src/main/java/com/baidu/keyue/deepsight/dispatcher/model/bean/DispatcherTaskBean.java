package com.baidu.keyue.deepsight.dispatcher.model.bean;

import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.model.dict.DispatcherStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.UUID;

/**
 * Created by IDEA
 *
 * <AUTHOR>
 * created at 2020/4/7 4:40 下午
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatcherTaskBean {
    private String id;

    private String botId;

    private String agentId;

    // 规则id
    private String ruleId;

    private String createUserId;

    private String createUserName;

    private Date created;

    private Date updated;

    private String params;

    private ModuleType module;

    private Integer retryLimit;

    private Integer retryCount;

    private Integer retryWaitSecond;
    
    private Date executeTime;

    private DispatcherStatus status;

    private String msg;

    private String tenantId;

    /**
     * 串行化id，如果模块设置为允许串行化（isSerial=true)
     * 则任务调度根据当前串行化id进行串行化，即相同串行化id的任务同时只会有一个任务处于运行态
     * 初始化逻辑：
     *  1、设置了该id，则任务中使用该id；如果未设置该id，看是否该模块设置了允许串行化，如果允许，则初始化为agentId，如果不允许，设置为uuid
     */
    private String serialKey;

    public static String initSerialKey(String agentId, String serialKey,
                                       ModuleType module) {
        // 1、如果serialKey不为空，使用serialKey
        if (StringUtils.isNotBlank(serialKey)) {
            return serialKey;
        }
        // 2、如果当前模块支持序列化，且agentId不为空，使用agentId
        if (module.isSerial() && StringUtils.isNotBlank(agentId)) {
            return agentId;
        }
        // 3、否则使用uuid
        return UUID.randomUUID().toString();
    }
}
