package com.baidu.keyue.deepsight.dispatcher.model.dict;

public enum ModuleType {

    /**
     * 测试使用类型，定时器会忽略该类型
     */
    MOCK_TEST(1, 0, 60000, 15, false),

    BAIDU_DATA_PULL_CENTER(5, 0, 2000, 15, false),

    CUSTOMER_GROUP_CAL_CENTER(5, 0, 2000, 15, false),

    CUSTOMER_FIELD_CLEAN_CENTER(5, 0, 2000, 15, false),

    CUSTOMER_DIFFUSION_CENTER(5, 0, 2000, 15, false),

    CUSTOMER_DIFFUSION_CHECK_CENTER(20, 0, 2000, 15, false),

    /**
     * LLM训练任务调度配置
     */
    LLM_MODEL_TRAIN_TASK_CENTER(50, 0, 2000, 15, false);


    /**
     * 默认并行度——单个节点同时执行的任务数
     */
    private final int parallelSize;
    /**
     * 默认重试次数——任务失败的可重试次数，不希望重试设置为0即可
     */
    private final int retryLimit;
    /**
     * 该模块扫描任务的时间，单位毫秒
     */
    private final int scanInMilli;
    /**
     * 基础重试等待时间，每次任务重试时间=重试次数*retryWaitSecond
     */
    private final int retryWaitSecond;
    /**
     * 该模块是否根据agentId串行执行，如果是，则同一个agentId同时只会有一个任务执行中。
     * 建议串行执行的任务，扫描时间可以设置大一点
     */
    private final boolean isSerial;

    ModuleType(int parallelSize,
               int retryLimit, int scanInMilli, int retryWaitSecond, boolean isSerial) {
        this.parallelSize = parallelSize;
        this.retryLimit = retryLimit;
        this.scanInMilli = scanInMilli;
        this.retryWaitSecond = retryWaitSecond;
        this.isSerial = isSerial;
    }

    public boolean isSerial() {
        return isSerial;
    }

    public int getRetryLimit() {
        return retryLimit;
    }

    public int getParallelSize() {
        return parallelSize;
    }

    public int getScanInMilli() {
        return scanInMilli;
    }

    public int getRetryWaitSecond() {
        return retryWaitSecond;
    }
}
