package com.baidu.keyue.deepsight.dispatcher.service.impl;

import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.dispatcher.annotation.TaskExecutor;
import com.baidu.keyue.deepsight.dispatcher.core.DispatcherExecutor;
import com.baidu.keyue.deepsight.dispatcher.model.dict.DispatcherConstants;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTask;
import com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean;
import com.baidu.keyue.deepsight.dispatcher.model.dict.ModuleType;
import com.baidu.keyue.deepsight.dispatcher.core.ModuleTypeWrapper;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherHandlerService;
import com.baidu.keyue.deepsight.dispatcher.service.DispatcherTaskManager;
import com.baidu.keyue.deepsight.dispatcher.core.ScheduleThreadFactory;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by IDEA
 *
 * <AUTHOR>
 * created at 2020/4/8 11:48 上午
 */
@Service
@Slf4j
public class DispatcherTaskManagerImpl implements DispatcherTaskManager {

    private final Map<ModuleType, DispatcherExecutor> executorServices = new HashMap<>();

    private final Map<ModuleType, ThreadPoolExecutor> threadPoolExecutors = new HashMap<>();

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DispatcherHandlerService dispatcherHandlerService;

    @Autowired
    private ModuleTypeWrapper moduleTypeWrapper;

    @Value("${dispatcher.expire.second:5}")
    private int defaultExpire;

    @Override
    public void init() {
        Map<String, DispatcherExecutor> executorMap = applicationContext.getBeansOfType(DispatcherExecutor.class);
        if (MapUtils.isNotEmpty(executorMap)) {
            executorMap.forEach(
                    (k, v) -> {
                        TaskExecutor annotation = AnnotationUtils.findAnnotation(v.getClass(), TaskExecutor.class);
                        log.info(v + " ---- " + v.getClass() + " 当前对象是否是代理对象 " + AopUtils.isAopProxy(v));
                        Optional.ofNullable(annotation).orElseThrow(
                                () -> new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "DISPATCHER_EXECUTOR_IMPLEMENT_ERROR"))
                        ;
                        ModuleType[] values = annotation.value();
                        log.info("TaskExecutor注解参数value[]  --- {}", JSONUtil.toJsonStr(values));
                        Arrays.stream(values).forEach(key -> executorServices.put(key, v));
                    }

            );
            initThreadPool();
        }
    }

    @Override
    public void submit(DispatcherTaskBean taskBean) {
        // 提交任务后存储，后续增加取消任务的逻辑
        threadPoolExecutors.get(taskBean.getModule())
                .submit(new DispatchTask(executorServices.get(taskBean.getModule()),
                        DispatcherTask.convertFromBean(taskBean)));
    }

    @Override
    public void checkExpire(int expire) {
        List<DispatcherTaskBean> expiredTasks = dispatcherHandlerService.checkExpire(expire);
        if (CollectionUtils.isNotEmpty(expiredTasks)) {
            log.info("check expire task, find expiredTask, id list: {}", StringUtils.join(expiredTasks, ","));
        }
        expiredTasks
                .stream()
                .map(DispatcherTask::convertFromBean)
                .forEach(this::fail);
    }

    @Override
    public int runningTaskCount(ModuleType module) {
        // TODO: 该数据可能不准确，因此需要自己实现运行线程数计算
        ThreadPoolExecutor executor = threadPoolExecutors.get(module);
        if (executor == null) {
            log.warn("未找到模块 [{}] 对应的线程池，返回任务数为 0", module);
            return 0;
        }
        return executor.getActiveCount();
    }

    private void initThreadPool() {
        for (ModuleType moduleType : executorServices.keySet()) {
            // 核心线程数与总线程大小一致，设置10倍并行度的等待队列，避免并发导致任务超过并行度
            int parallelSize = moduleTypeWrapper.getParallelSize(moduleType);
            ThreadPoolExecutor executor =
                    new ThreadPoolExecutor(parallelSize,
                            parallelSize,
                            1L, TimeUnit.MILLISECONDS,
                            new ArrayBlockingQueue<>(parallelSize * 10),
                            new ScheduleThreadFactory(
                                    moduleType.name() + DispatcherConstants.CORE_EXECUTOR_FACTORY_NAME),
                            new ThreadPoolExecutor.CallerRunsPolicy());
            // 线程执行完成后回收
            executor.allowCoreThreadTimeOut(true);
            threadPoolExecutors.put(moduleType, executor);

            log.info("put task [{}] success", moduleType);
        }
    }

    public void fail(DispatcherTask task, boolean isCallByExecutor) {
        log.error("execute task fail. task id is {}, call by {}", task.getId(), isCallByExecutor ? "executor" : "expire"
                + " checker");
        complete(task, false);
    }

    @Override
    public void fail(DispatcherTask task) {
        // 给过期检查器使用的失败回调
        fail(task, false);
    }

    @Override
    public void cancel(DispatcherTask task) {
        DispatcherExecutor executorService = executorServices.get(task.getModuleType());
        try {
            log.info("execute task cancel call back. task id is {}", task.getId());
            executorService.cancel(task);
        } catch (Exception e) {
            log.error("execute task fail when call cancel method of module {}, task id is {}", task.getModuleType(),
                    task.getId(), e);
        }
        try {
            dispatcherHandlerService.cancel(task.getId(), task.getAgentId(), task.getModuleType());
        } catch (Exception e) {
            log.error("execute task fail when call cancel method for handler, task id is {}", task.getId(), e);
        }
    }

    @Override
    public void success(DispatcherTask task) {
        log.info("execute task success call back. task id is {}", task.getId());
        complete(task, true);
    }

    private void complete(DispatcherTask task, boolean isSuccess) {
        DispatcherExecutor executorService = executorServices.get(task.getModuleType());
        // 执行回调
        try {
            if (isSuccess) {
                executorService.success(task);
            } else {
                executorService.fail(task);
            }
        } catch (Exception e) {
            log.error("execute task fail when call {} method of module {}, task id is {}",
                    isSuccess ? DispatcherConstants.SUCCESS : DispatcherConstants.FAIL, task.getModuleType(),
                    task.getId(), e);
        } finally {
            try {
                executorService.complete(task);
                log.info("execute task complete call back. task id is {}", task.getId());
            } catch (Exception e) {
                log.error("execute task fail when call complete method of module {} task id is {}",
                        task.getModuleType(), task.getId(), e);
            }
        }

        try {
            if (isSuccess) {
                dispatcherHandlerService.success(task.getId(), task.getAgentId(), task.getModuleType());
            } else {
                dispatcherHandlerService.fail(task.getId(), task.getAgentId(), task.getModuleType());
            }
        } catch (Exception e) {
            log.error("execute task fail when call {} method for handler,task id is {}",
                    isSuccess ? DispatcherConstants.SUCCESS : DispatcherConstants.FAIL, task.getId(), e);
        }
    }

    private class DispatchTask extends Thread {

        private DispatcherExecutor executorService;
        private DispatcherTask task;

        private TickerTask ticker;

        private DispatchTask(
                DispatcherExecutor executorService,
                DispatcherTask task) {
            this.executorService = executorService;
            this.task = task;
        }

        private void runTicker() {
            ticker = new TickerTask(task);
            // 设置为守护线程，可以监听线程的状态
            ticker.setDaemon(true);
            // 设置ticker的名字与启动线程的名字关联，便于分析日志定位问题
            ticker.setName(Thread.currentThread().getName() + DispatcherConstants.TICKER_THREAD_NAME);
            ticker.start();
        }

        @Override
        public void run() {
            log.debug("task runner, module is [{}], taskId is {}, start", task.getModuleType(), task.getId());
            runTicker();
            // do task
            try {
                executorService.execute(task);
                // 执行成功回调
                success(task);
            } catch (InterruptedException e) {
                log.error("run task error, ", e);
                cancel(task);
            } catch (Exception e) {
                log.error("run task error, ", e);
                // 执行失败回调
                fail(task, true);
            } finally {
                // 中断守护线程
                ticker.interrupt();
            }
        }

    }

    class TickerTask extends Thread {

        private DispatcherTask task;
        private boolean interrupt = false;

        TickerTask(DispatcherTask task) {
            this.task = task;
        }

        @Override
        public void interrupt() {
            interrupt = true;
        }

        @Override
        public void run() {
            // do ticker
            // 1、承担取消任务的作用
            // 2、承担定时检查的作用
            try {
                while (!interrupt) {
                    // TODO: 取消任务
                    // 定时刷新任务时间
                    dispatcherHandlerService.refreshTask(task.getId(), task.getAgentId(), task.getModuleType());
                    TimeUnit.SECONDS.sleep(defaultExpire / 3);
                }
            } catch (InterruptedException e) {
                // 允许退出
                log.error("ticker error, ", e);
            }
        }

    }

}
