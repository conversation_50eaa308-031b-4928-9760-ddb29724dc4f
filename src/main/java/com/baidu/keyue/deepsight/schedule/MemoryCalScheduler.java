package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.schedule.task.MemoryCalAsyncTask;
import com.baidu.keyue.deepsight.service.memory.MemoryCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class MemoryCalScheduler {

    @Value("${switch.memoryCalScheduler:false}")
    private boolean memoryCalScheduler;

    @Autowired
    private MemoryCalculateService memoryService;

    @Autowired
    private MemoryCalAsyncTask memoryCalAsyncTask;

    @Scheduled(fixedDelay = 60000L, initialDelay = 60000L)
    public void memoryCalFakeRealtimeScheduler() {
        if (!memoryCalScheduler) {
            log.info("memoryCalFakeRealtime is disabled. quit...");
            return;
        }

        log.info("memoryCalFakeRealtime start...");
        try {
            taskExec(TriggerModeEnum.REALTIME);
        } catch (Exception e) {
            log.error("memoryCalFakeRealtime exec task failed, ", e);
        }
        log.info("memoryCalFakeRealtime exec finished. quit...");
    }

    @Scheduled(cron = "0 0 * * * *")
    public void memoryCalCronScheduler() {
        if (!memoryCalScheduler) {
            log.info("memoryCalSchedulerInBSCMod is disabled. quit...");
            return;
        }

        log.info("memoryCalSchedulerInBSCMod start...");
        try {
            taskExec(TriggerModeEnum.CRON);
        } catch (Exception e) {
            log.error("memoryCalSchedulerInBSCMod exec task failed, ", e);
        }
        log.info("memoryCalSchedulerInBSCMod exec finished. quit...");
    }


    private void taskExec(TriggerModeEnum triggerMode) {
        List<Pair<MemoryExtractWithBLOBs, TaskInfo>> memoryExtractTaskPairs =
                memoryService.pullWaitExecMemoryTask(triggerMode);
        if (CollectionUtils.isEmpty(memoryExtractTaskPairs)) {
            log.info("memoryCalScheduler got empty diffusionTask. quit...");
            return;
        }

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (Pair<MemoryExtractWithBLOBs, TaskInfo> pair : memoryExtractTaskPairs) {
            futures.add(memoryCalAsyncTask.processPair(pair, triggerMode));
        }
        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

}
