package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.schedule.task.BaiduDataPullAsyncTask;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class BaiduDataPullSchedule {

    @Value("${switch.pullBaiduData:false}")
    private boolean pullBaiduData;

    @Autowired
    private BaiduUserDataService baiduUserDataService;

    @Autowired
    private BaiduDataPullAsyncTask dataPullAsyncTask;

    /**
     * 从百度拉取用户真实数据
     * 每一个小时触发一次
     */
    @Scheduled(cron = "${cron.pullBaiduData:0 0 * * * *}")
    public void pullDataScheduler() {
        if (!pullBaiduData) {
            log.info("pullDataScheduler is disabled. quit...");
            return;
        }

        log.info("pullDataScheduler start...");
        try {
            // 每次执行都需要刷新下redis中mobile解码的secretKey
            baiduUserDataService.refreshSecretKey();

            List<String> allUserTable = baiduUserDataService.getAllUserTable();
            log.info("allUserTable: {}", allUserTable);
            if (CollectionUtils.isEmpty(allUserTable)) {
                log.info("pullDataScheduler ext finished, no user table...");
                return;
            }

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (String table : allUserTable) {
                futures.add(dataPullAsyncTask.processTable(table));
            }

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("pullDataScheduler exec task failed, ", e);
        }

        log.info("pullDataScheduler exec finished. quit...");
    }
}
