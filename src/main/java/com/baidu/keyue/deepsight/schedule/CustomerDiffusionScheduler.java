package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.RedisConfiguration;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.schedule.task.CustomerDiffusionCheckAsyncTask;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className CustomerDiffusionScheduler
 * @description 客群扩散任务
 * @date 2025/3/24 17:07
 */
@Slf4j
@Component
public class CustomerDiffusionScheduler {

    @Value("${switch.customerDiffusion:false}")
    private boolean customerDiffusion;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private GroupDiffusionCalculateService diffusionCalculateService;

    @Autowired
    private CustomerDiffusionCheckAsyncTask diffusionCheckAsyncTask;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 定时触发客群扩散任务提交(处理待执行的任务)
     * 每两分钟触发一次：
     * 1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     * 2、规则解析 && 提交执行
     * 3、生成执行记录
     * 4、更新 next_exec_date 执行时间
     */
    @Scheduled(cron = "${cron.customerDiffusion:0 0 * * * *}")
    public void customerDiffusionScheduler() {
        if (!customerDiffusion) {
            log.info("customerDiffusionScheduler is disabled. quit...");
            return;
        }

        // get lock
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "customerDiffusionRequestScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerDiffusionScheduler get lock failed.");
            return;
        }

        try {
            // 获取除已完成的任务，提交给扩散模型
            List<Pair<CustomerDiffusionTask, TaskInfo>> diffusionTasks =
                    diffusionCalculateService.pullTaskByStatus(TaskExecStatusEnum.SUCCESS);
            if (CollectionUtils.isEmpty(diffusionTasks)) {
                log.info("customerDiffusionScheduler got empty diffusionTask. quit...");
                return;
            }
            diffusionTasks.forEach(pair -> {
                // 1、任务执行
                // 2、修改任务状态为执行中
                try {
                    diffusionCalculateService.execGroupDiffusion(pair.getLeft().getId(), Constants.SCHEDULER_SYSTEM_USER_ID);
                } catch (Exception e) {
                    log.error("customerDiffusionScheduler exec group diffusion task failed, ", e);
                }
            });


        } catch (Exception e) {
            log.error("customerDiffusionScheduler exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("customerDiffusionScheduler exec finished. quit...");
    }

    /**
     * 定时检查客群扩散模型的donefile(轮训计算中的任务)
     * 每一分钟触发一次：
     * 1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     * 2、规则解析 && 提交执行
     * 3、生成执行记录
     * 4、更新 next_exec_date 执行时间
     */
    @Scheduled(cron = "${cron.customerDiffusionCheck:0 0/1 * * * *}")
    public void customerDiffusionCheckScheduler() {
        if (!customerDiffusion) {
            log.info("customerDiffusionCheckScheduler of result scheduler is disabled. quit...");
            return;
        }
        // get lock
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "customerDiffusionHandleScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerDiffusionCheckScheduler of result get lock failed.");
            return;
        }


        // 查业务任务在执行中的，查出task_scheduler id
        // 获取计算中的任务，提交给扩散模型
        log.info("customerDiffusionCheckScheduler start...");
        List<CustomerDiffusionTask> customerDiffusionTasks = diffusionCalculateService.pullRunningDiffusionTask();
        if (CollectionUtils.isEmpty(customerDiffusionTasks)) {
            log.info("customerDiffusionCheckScheduler got empty diffusionTask. quit...");
        }

        try {
            Map<String, List<CustomerDiffusionTask>> cgid2ListMap = customerDiffusionTasks.stream()
                    .collect(Collectors.groupingBy(
                                    // task -> task.getTenantId() + ":" + task.getCustomerGroupId(),
                                    // CustomerDiffusionTask::getCustomerGroupId,
                                    CustomerDiffusionTask::getTenantId,
                                    Collectors.collectingAndThen(
                                            Collectors.toMap(
                                                    CustomerDiffusionTask::getTaskId,
                                                    e -> e,
                                                    (e1, e2) -> e1
                                            ),
                                            m -> new ArrayList<>(m.values())
                                    )
                            )
                    )
                    // 过滤掉空列表
                    .entrySet()
                    .stream()
                    .filter(entry -> !entry.getValue().isEmpty())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            log.info("customerDiffusionCheckScheduler cgid2ListMap: {}", JsonUtils.toJsonWithOutException(cgid2ListMap));
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (Map.Entry<String, List<CustomerDiffusionTask>> entry : cgid2ListMap.entrySet()) {
                List<Long> taskIds = entry.getValue()
                        .stream()
                        .map(CustomerDiffusionTask::getTaskId)
                        .distinct()
                        .toList();
                futures.add(diffusionCheckAsyncTask.processTasksByTenantId(taskIds));
            }
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("customerDiffusionCheckScheduler of result exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("customerDiffusionCheckScheduler of result exec finished. quit...");
    }


}
