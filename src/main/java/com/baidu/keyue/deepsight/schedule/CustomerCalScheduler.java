package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.config.RedisConfiguration;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupCalculateService;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CustomerCalScheduler {

    @Value("${switch.customerCalSchedulerWithBSC:false}")
    private boolean customerCalSchedulerWithBSC;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private CustomerGroupService customerGroupService;

    @Autowired
    private CustomerGroupCalculateService calculateService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 定时触发标签生产任务(BSC 模式)
     * <p>
     * 每一个小时触发一次：
     * 1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     * 2、规则解析 && 提交执行
     * 3、生成执行记录
     * 4、更新 next_exec_date 执行时间
     */
    @Scheduled(cron = "${cron.customerCalSchedulerWithBSC:0 0 * * * *}")
    public void customerCalSchedulerInBSCMod() {
        if (!customerCalSchedulerWithBSC) {
            log.info("customerCalSchedulerInBSCMod is disabled. quit...");
            return;
        }

        // get lock
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "customerCalScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerCalSchedulerInBSCMod get lock failed.");
            return;
        }

        log.info("customerCalSchedulerInBSCMod start...");
        List<Pair<CustomerGroup, TaskInfo>> labelTaskPairs = calculateService.pullWaitExecCustomerTask();
        if (CollectionUtils.isEmpty(labelTaskPairs)) {
            log.info("customerCalSchedulerInBSCMod got empty labelTaskPairs quit...");
            return;
        }

        try {
            labelTaskPairs.forEach(pair -> {
                calculateService.execByScheduler(pair.getLeft(), pair.getRight());
            });
        } catch (Exception e) {
            log.error("customerCalSchedulerInBSCMod exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("customerCalSchedulerInBSCMod exec finished. quit...");
    }

    /**
     * 清理已标记删除的 doris 宽表标签
     * 每天凌晨 3 点执行，捞更新时间小于 24小时，状态为删除的标签
     */
    @Scheduled(cron = "${cron.customerFieldClean:0 0 3 * * *}")
    public void customerFieldClean() {
        if (!customerCalSchedulerWithBSC) {
            log.info("customerFieldClean is disabled. quit...");
            return;
        }
        // get lock
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "customerFieldClean", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerFieldClean get lock failed.");
            return;
        }

        log.info("customerFieldClean start...");
        List<CustomerGroup> deletedLabels = customerGroupService.queryDeletedLabel(86400);
        if (CollectionUtils.isEmpty(deletedLabels)) {
            log.info("customerFieldClean got empty deletedLabel. quit...");
            return;
        }

        try {
            deletedLabels.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getTenantId()))
                    .forEach(item -> {
                        log.debug(String.format("customerFieldClean clean label, id: %s", item.getId()));
                        try {
                            // TODO 需要解决频繁修改表结构问题，考虑是否可以统一放到 json 字段中
                            calculateService.invalidCustomerDorisFieldClear(item);
                        } catch (Exception e) {
                            log.error(String.format("customerFieldClean clean label failed, id: %s, ", item.getId()), e);
                        }
                    });
        } catch (Exception e) {
            log.error("customerFieldClean exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


}
