package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.schedule.task.DataPredictionAsyncTask;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionCalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class DataPredictionCalScheduler {

    @Value("${switch.dataPredictionSchedulerWithBSC:false}")
    private boolean dataPredictionSchedulerWithBSC;

    @Autowired
    private DataPredictionCalService dataPredictionCalService;

    @Autowired
    private DataPredictionAsyncTask dataPredictionAsyncTask;

    /**
     * 定时触发数据增强预测任务(BSC 模式)
     * 每一个小时触发一次：
     * 1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     * 2、规则解析 && 提交执行
     * 3、生成执行记录
     * 4、更新 next_exec_date 执行时间
     */
    @Scheduled(cron = "${cron.dataPredictionSchedulerWithBSC:0 0 * * * *}")
    public void dataPredictionScheduler() {
        if (!dataPredictionSchedulerWithBSC) {
            log.info("dataPredictionScheduler is disabled. quit...");
            return;
        }

        log.info("dataPredictionScheduler start...");
        List<Pair<DataPredictionSourceWithBLOBs, TaskInfo>> predictionTaskPairs =
                dataPredictionCalService.pullWaitExecTask();
        if (CollectionUtils.isEmpty(predictionTaskPairs)) {
            log.info("dataPredictionScheduler got empty paris. quit...");
            return;
        }

        try {
            log.info("dataPredictionScheduler pairs-size: {}", predictionTaskPairs.size());
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (Pair<DataPredictionSourceWithBLOBs, TaskInfo> pair : predictionTaskPairs) {
                futures.add(dataPredictionAsyncTask.processPair(pair));
            }
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("dataPredictionScheduler exec task failed, ", e);
        }

        log.info("dataPredictionScheduler exec finished. quit...");
    }
}
