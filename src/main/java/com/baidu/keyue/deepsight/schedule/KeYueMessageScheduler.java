package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.RedisConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.SOPStatusEnum;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSOPServiceImpl;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSopMetricService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tool.MessageService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.kybase.sdk.message.enums.MessageTypeEnum;
import com.baidu.kybase.sdk.message.vo.MessageBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @className KeYueMessageScheduler
 * @description Keyue消息推送
 * @date 2025/5/16 16:55
 */
@Slf4j
@Component
public class KeYueMessageScheduler {

    @Autowired
    private MessageService messageService;

    @Autowired
    private TenantInfoService tenantInfoService;

    @Autowired
    private AiobSOPServiceImpl aiobSOPService;

    @Autowired
    private AiobSopMetricService aiobSopMetricService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Scheduled(cron = "0 0 * * * *")
    public void sendMessage() {
        // get lock
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "keYueAopMessagePushScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerDiffusionScheduler get lock failed.");
            return;
        }
        try {
            List<TenantInfo> tenantInfos = tenantInfoService.getAllTenantInfo();
            // 当前时间
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            String dateKey = DatetimeUtils.pureDateFormat(now);
            for (TenantInfo tenantInfo : tenantInfos) {
                // 获取租户下所有的sop告警配置
                List<SopUserConfig> sopConfigs = aiobSOPService.getSopConfigByStatus(tenantInfo.getTenantid(), SOPStatusEnum.COMPLETED.getCode());
                if (CollectionUtils.isEmpty(sopConfigs)) {
                    continue;
                }
                // 更新最新的任务状态
                updateTaskStatus(tenantInfo.getTenantid(), sopConfigs);
                // 排除已完成状态的任务
                sopConfigs = aiobSOPService.getSopConfigByStatus(tenantInfo.getTenantid(), SOPStatusEnum.COMPLETED.getCode());
                // 计算每小时用户挂断率
                for (SopUserConfig userConfig : sopConfigs) {
                    // 根据taskId统计所有节点的uv
                    List<Map<String, Object>> uvMap = aiobSopMetricService.getNodeUvByTaskIdSQL(tenantInfo.getTenantid(),
                            userConfig.getTaskId(), null, null);
                    // 根据taskId统计所有节点的挂断数
                    List<Map<String, Object>> hangUpCntMap = aiobSopMetricService.getNodeHangUpByTaskIdSQL(tenantInfo.getTenantid(),
                            userConfig.getTaskId(), null, null);
                    if (CollectionUtils.isEmpty(uvMap) || CollectionUtils.isEmpty(hangUpCntMap)) {
                        log.warn("Failed to calculate user hang-up rate and UV for tenantId: {}, taskId: {}"
                                , tenantInfo.getTenantid(), userConfig.getTaskId());
                        continue;
                    }
                    for (Map<String, Object> uv : uvMap) {
                        String nodeId = uv.get("node_id").toString();
                        Optional<Map<String, Object>> first = hangUpCntMap.stream().filter(hangUp
                                -> hangUp.get("node_id").toString().equals(nodeId)).findFirst();
                        if (StringUtils.isEmpty(nodeId) || first.isEmpty()) {
                            continue;
                        }
                        Long hangUpUV = Long.valueOf(first.get().get("node_uv").toString());
                        Long nodeUv = Long.valueOf(uv.get("node_uv").toString());
                        // 统计历史挂断率，同任务同节点每天最多报警一次
                        String key = tenantInfo.getTenantid() + "_" + userConfig.getTaskId() + "_" + nodeId + "_" + dateKey;
                        if ((double) hangUpUV / nodeUv > userConfig.getWarningThreshold() * 0.01) {
                            if (redisson.getBucket(key).isExists()) {
                                log.info("message has already been pushed, key is {}", key);
                                continue;
                            }
                            MessageBody messageBody = new MessageBody();
                            // 填入主账号accountId（也叫cloudId）， 将发送给发送给该主账号用户及其对应租户的所有超级管理员（租户管理员）
                            messageBody.setAccountId(tenantInfo.getAccountid());
                            // 设置消息类型为服务消息
                            messageBody.setType(MessageTypeEnum.SERVICE_MSG.getType());
                            // 设置消息标题
                            messageBody.setTitle("sop节点挂断异常提醒");
                            // 设置消息内容。注意，消息体里面包含点击消息的跳转文案和链接，消息内容为富文本
                            // 查询任务名称
                            List<Map<String, Object>> taskNameMap = dorisService.selectList(
                                    ORMUtils.generateQueryTaskNameByTaskIdSQL(Constants.DORIS_AIOB_SESSION_TABLE + "_" + tenantInfo.getTenantid(),
                                            userConfig.getTaskId()));
                            String stepName = getStepName(tenantInfo.getTenantid(),
                                    taskNameMap.get(0), nodeId);
                            if (StringUtils.isEmpty(stepName)) {
                                log.warn("Failed to calculate user hang-up rate and UV for taskId: {}, nodeId: {}",
                                        userConfig.getTaskId(), nodeId);
                                continue;
                            }
                            int year = calendar.get(Calendar.YEAR);
                            int month = calendar.get(Calendar.MONTH) + 1;
                            int day = calendar.get(Calendar.DAY_OF_MONTH);
                            int hour = calendar.get(Calendar.HOUR_OF_DAY);
                            String content = String.format("[任务:%s][节点:%s][时间:%s-%s-%s %s时]挂断率高于阈值触发提醒，请及时查看",
                                    taskNameMap.get(0).get("taskName").toString(),
                                    stepName,
                                    year, month, day,
                                    hour);
                            messageBody.setContent(content);
                            redisson.getBucket(key)
                                    .set("1", 24 - hour, TimeUnit.HOURS);
                            messageService.pushMessage(tenantInfo.getAccountid(), messageBody);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("keYueAopMessagePushScheduler exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info("keYueAopMessagePushScheduler exec finished. quit...");
    }

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }


    public void updateTaskStatus(String tenantId, List<SopUserConfig> sopUserConfigs) {
        for (SopUserConfig sopUserConfig : sopUserConfigs) {
            Integer status = aiobSOPService.getSopConfigStatus(tenantId, sopUserConfig.getTaskId());
            if (SOPStatusEnum.COMPLETED.getCode().equals(status)) {
                // 更新已完成的状态
                aiobSOPService.updateStatus(tenantId, SOPStatusEnum.COMPLETED, sopUserConfig.getTaskId());
            }
        }
    }

    public String getStepName(String tenantId, Map<String, Object> map, String nodeId) {
        try {
            // 快捷场景
            if (("5").equals(map.get("robotScene").toString())) {
                AiobSopMeta aiobSopMetas = aiobSOPService.getFrontNodeInfoWithNodeId(nodeId, tenantId);
                return aiobSopMetas.getStepName();
            } else if (("6").equals(map.get("robotScene").toString())) {
                // 查询agentId
                List<Map<String, Object>> agentIdRes = aiobSopMetricService.getAgentIdSQL(tenantId, map.get("sessionId").toString(), nodeId);
                AiobDiagramVersionRecordViewResp resp = aiobSOPService.getDiagramRecords(agentIdRes.get(0).get("agent_id").toString()
                        , agentIdRes.get(0).get("version_id").toString());
                return resp.getData().getTopicList().get(0).getContent()
                        .getNodes().get(nodeId).getData().getLabel();
            }
            return null;
        } catch (Exception e) {
            log.error("getStepName error", e);
            return null;
        }
    }

}