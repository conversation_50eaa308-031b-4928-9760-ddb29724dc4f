package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.config.RedisConfiguration;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MetricAggScheduler {

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 统计外呼通话记录 90天内的统计指标
     */
    @Scheduled(cron = "0 0 2 * * *")
    public void aiobSessionMetricAgg() {
        log.info("aiobSessionMetricAgg task start");
        String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "aiobSessionMetricAgg", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("aiobSessionMetricAgg get lock failed.");
            return;
        }

        try {
            // 通过 show tables 获取aiob_conversation_session_agg_（天级别指标明细）
            List<String> sessionAggTables = aiobSessionMetricAggService.getSessionAggTables();
            sessionAggTables.forEach(table -> {
                try {
                    aiobSessionMetricAggService.aiobSessionMetricAggExec(table);
                } catch (Exception e) {
                    log.error("aiobSessionMetricAgg exec task failed, table: {}", table, e);
                }
                log.info("aiobSessionMetricAgg exec task finished, table: {}", table);
            });

            // 统计 90天的外呼通话统计指标
        } catch (Exception e) {
            log.error("try { exec task failed, ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        log.info("aiobSessionMetricAgg task finished.");
    }
}
