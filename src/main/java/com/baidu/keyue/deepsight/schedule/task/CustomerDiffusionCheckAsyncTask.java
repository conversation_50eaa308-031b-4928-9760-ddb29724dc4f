package com.baidu.keyue.deepsight.schedule.task;

import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class CustomerDiffusionCheckAsyncTask {

    @Autowired
    private GroupDiffusionCalculateService diffusionCalculateService;

    @Async("customerDiffusionCheckTaskExecutor")
    public CompletableFuture<Void> processTasksByTenantId(List<Long> taskIds) {
        for (Long taskId : taskIds) {
            // 任务修改为计算完成
            try {
                diffusionCalculateService.fetchGroupDiffusionResult(taskId);
            } catch (Exception e) {
                log.error("customerDiffusionCheckScheduler of result exec task failed, ", e);
            }
        }
        return CompletableFuture.completedFuture(null);
    }

}
