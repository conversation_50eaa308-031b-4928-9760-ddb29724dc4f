<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.baidu.keyue.deepsight.dispatcher.dao.DispatcherDao">
    <!--resultMap-->
    <resultMap type="com.baidu.keyue.deepsight.dispatcher.model.bean.DispatcherTaskBean" id="DispatcherTask">
        <id property="id" column="id"/>
        <result property="botId" column="bot_id"/>
        <result property="agentId" column="agent_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="created" column="created"/>
        <result property="updated" column="updated"/>
        <result property="params" column="params"/>
        <result property="module" column="module"/>
        <result property="retryLimit" column="retry_limit"/>
        <result property="retryCount" column="retry_count"/>
        <result property="retryWaitSecond" column="retry_wait_second"/>
        <result property="executeTime" column="execute_time"/>
        <result property="status" column="task_status"/>
        <result property="msg" column="msg"/>
        <result property="serialKey" column="serial_key"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="primaryColumns">
        <![CDATA[
            id,
            bot_id,
            agent_id,
            rule_id,
            create_user_id,
            create_user_name,
            created,
            updated,
            params,
            module,
            retry_limit,
            retry_count,
            retry_wait_second,
            execute_time,
            task_status,
            msg,
            serial_key,
            tenant_id
        ]]>
    </sql>

    <sql id="rulePrimaryColumns">
        <![CDATA[
            id,
            agent_id,
            bot_id,
            create_user_id,
            create_user_name,
            created,
            updated,
            params,
            module,
            cron,
            task_num,
            active_start_time,
            active_end_time
        ]]>
    </sql>

    <insert id="insert">
        INSERT INTO dispatcher_task (<include refid="primaryColumns"/>)
        VALUES (#{id}, #{botId}, #{agentId}, #{ruleId}, #{createUserId}, #{createUserName}, #{created}, #{updated},
        #{params}, #{module}, #{retryLimit}, #{retryCount}, #{retryWaitSecond}, #{executeTime}, #{status}, #{msg},
        #{serialKey}, #{tenantId});
    </insert>

    <update id="update">
        UPDATE dispatcher_task
        SET id = #{id}, updated = now()

        <if test="status != null">
            , `task_status` = #{status}
        </if>
        <if test='msg != null and msg != ""'>
            , `msg` = #{msg}
        </if>
        <if test="executeTime != null">
            , `execute_time` = #{executeTime}
        </if>
        <if test="retryCount != null">
            , `retry_count` = #{retryCount}
        </if>
        WHERE id = #{id}
        <if test="agentId != null">
            AND agent_id = #{agentId}
        </if>
        AND `module` = #{module};
    </update>

    <select id="findOlderTaskByStatus" resultMap="DispatcherTask">
        SELECT
        <include refid="primaryColumns"/>
        FROM dispatcher_task
        WHERE `task_status` = #{status} AND `execute_time` &lt; now() AND `module` = #{module}
        limit 1;
    </select>

    <select id="findOlderTaskByStatusAndUniqueSerialKey" resultMap="DispatcherTask">
        SELECT
        <include refid="primaryColumns"/>
        FROM `dispatcher_task` WHERE `task_status` = 'CREATE'
        AND `execute_time` &lt; now() AND `module` = #{module}
        AND `serial_key` NOT IN (
            SELECT max(`serial_key`) as `serial_key` FROM `dispatcher_task` WHERE `task_status` = 'RUNNING' AND `module` = #{module}
                GROUP BY `serial_key`)
        limit 1;
    </select>

    <select id="findById" resultMap="DispatcherTask">
        SELECT
        <include refid="primaryColumns"/>
        FROM dispatcher_task
        WHERE id = #{id}
        <if test="agentId != null">
            AND agent_id = #{agentId}
        </if>
        AND `module` = #{module};
    </select>

    <select id="findByAgentId" resultMap="DispatcherTask">
        SELECT
        <include refid="primaryColumns"/>
        FROM dispatcher_task
        where agent_id = #{agentId}
    </select>

    <select id="findExpiredTask" resultMap="DispatcherTask">
        SELECT
        <include refid="primaryColumns"/>
        FROM `dispatcher_task`
        WHERE `updated` &lt; #{expire} AND `task_status` = #{status};
    </select>

    <update id="tryAcquireTask">
        UPDATE dispatcher_task
        SET `task_status` = 'RUNNING', updated = now()
        WHERE id = #{id} AND `task_status` = 'CREATE';
    </update>

    <select id="countTaskByStatusAndAgentId" resultType="int">
        SELECT
        count(1)
        FROM `dispatcher_task`
        WHERE
        `module` = #{module} AND `task_status` = #{status}
        <if test="agentId != null">
            AND `agent_id` = #{agentId}
        </if>
        ;
    </select>

    <delete id="deleteCreateTaskByAgentId">
        DELETE FROM `dispatcher_task`
        WHERE `agent_id` = #{agentId} AND `task_status` = 'CREATE';
    </delete>

    <select id="findByAgentIdAndModuleWithStatus" resultMap="DispatcherTask">
        SELECT
        <include refid="primaryColumns"/>
        FROM `dispatcher_task`
        WHERE `agent_id` = #{agentId} AND `module` = #{module}
        <if test="list != null">
            AND (`task_status` in
            <foreach collection="list" item="status" index="index" open="(" close=")"
                     separator=",">
                #{status}
            </foreach>)
        </if>
    </select>

    <insert id="batchInsert">
        INSERT INTO dispatcher_task (<include refid="primaryColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.id}, #{item.botId}, #{item.agentId}, #{item.ruleId}, #{item.createUserId}, #{item.createUserName}, #{item.created}, #{item.updated},
            #{item.params}, #{item.module}, #{item.retryLimit}, #{item.retryCount}, #{item.retryWaitSecond}, #{item.executeTime}, #{item.status}, #{item.msg},
            #{item.serialKey}, #{item.tenantId})
        </foreach>

    </insert>


</mapper>
