
-- ----------------------------
-- Table structure for dispatcher_task
-- ----------------------------
DROP TABLE IF EXISTS `dispatcher_task`;
CREATE TABLE `dispatcher_task` (
   `id` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '任务id，uuid生成',
   `agent_id` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务所属agent',
   `bot_id` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务所属bot',
   `rule_id` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务生成规则id',
   `create_user_id` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '任务创建用户id',
   `create_user_name` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '任务创建用户名',
   `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建任务时间',
   `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
   `params` text COLLATE utf8mb4_bin COMMENT '任务参数',
   `module` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '模块名称',
   `retry_limit` int(11) NOT NULL DEFAULT '0' COMMENT '任务失败重试上限，0表示不重试',
   `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '任务失败重试次数',
   `retry_wait_second` int(11) NOT NULL DEFAULT '0' COMMENT '任务失败重试等待时间，等待时间=retry_wait_second*retry_count',
   `execute_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务开始执行时间，系统时间大于该时间任务才可能被调度',
   `msg` varchar(2048) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '记录错误信息',
   `task_status` varchar(45) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '任务状态，create、running、ready、abort、fail、finish',
   `serial_key` varchar(64) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '根据该id进行串行化',
   `tenant_id` varchar(45) COLLATE utf8mb4_bin DEFAULT NULL,
   UNIQUE KEY `task_uniq_key` (`id`,`agent_id`,`module`),
   KEY `agent_id` (`agent_id`),
   KEY `module` (`module`),
   KEY `execute_time` (`execute_time`),
   KEY `task_status` (`task_status`),
   KEY `rule_id` (`rule_id`) USING BTREE,
   KEY `serialKeyIndex` (`serial_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

