CREATE TABLE IF NOT EXISTS `alert_record` (
    `tenant_id` VARCHAR(255) NOT NULL COMMENT '租户ID',
    `config_type` VARCHAR(20) NOT NULL COMMENT '配置类型(task/caller/robot)',
    `config_target` VARCHAR(50) NOT NULL COMMENT '配置目标(task_id/caller_num/robot_id)',
    `alarm_date` DATE NOT NULL COMMENT '告警日期:yyyy-MM-dd',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    ) ENGINE=OLAP DUPLICATE KEY(`tenant_id`,`config_type`,`config_target`,`alarm_date`) COMMENT '呼通分析接通率阈值告警记录' DISTRIBUTED BY HASH(`config_target`) BUCKETS 10 PROPERTIES(
   "replication_allocation"="tag.location.default: 3",
   "is_being_synced"="false",
   "storage_medium"="hdd",
   "storage_format"="V2",
   "light_schema_change"="true",
   "disable_auto_compaction"="false",
   "enable_single_replica_compaction"="false",
   "enable_mow_light_delete"="false"
);
ALTER TABLE `global_default_session` ADD COLUMN `callerNum` varchar(255) NULL COMMENT '主叫号码';
ALTER TABLE `global_default_session` ADD COLUMN `dicCategory` varchar(255) NULL COMMENT '字典类别-未接通类别';
ALTER TABLE `global_default_session` ADD COLUMN `dicName` varchar(255) NULL COMMENT '字典名称-未接通名称';
ALTER TABLE `global_default_session` ADD COLUMN `is_auto_answer` tinyint NULL COMMENT '是否为小秘书：1是，0否';
ALTER TABLE `global_default_session` ADD COLUMN `lineStatus` varchar(255) NULL DEFAULT 'ENABLED' COMMENT '号线状态：ALL(全部状态)/ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)';
ALTER TABLE `global_default_session` ADD COLUMN `taskStatus` tinyint NULL COMMENT '外呼任务:1-待启动/2-执行中/3-已暂停/4-已完成/5-已终止';
ALTER TABLE `global_default_session` ADD COLUMN `callerCity` varchar(255) NULL COMMENT '主叫号码城市';