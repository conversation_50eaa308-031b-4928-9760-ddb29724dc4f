ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `callerNum` varchar(255) NULL COMMENT '主叫号码';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `dicCategory` varchar(255) NULL COMMENT '字典类别-未接通类别';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `dicName` varchar(255) NULL COMMENT '字典名称-未接通名称';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `is_auto_answer` tinyint NULL COMMENT '是否为小秘书：1是，0否';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `lineStatus` varchar(255) NULL DEFAULT 'ENABLED' COMMENT '号线状态：ALL(全部状态)/ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `taskStatus` tinyint NULL COMMENT '外呼任务:1-待启动/2-执行中/3-已暂停/4-已完成/5-已终止';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `callerCity` varchar(255) NULL COMMENT '主叫号码城市';