CREATE TABLE IF NOT EXISTS `id_mapping_^&` (
    `oneId` varchar(64) NOT NULL COMMENT '唯一id',
    `user_id` array<varchar(255)> NULL default '[]',
    `mobile` array<varchar(255)> NULL default '[]',
    `BAIDUID` array<varchar(255)> NULL default '[]',
    `UNIONID` array<varchar(255)> NULL default '[]',
    `DEVICEID` array<varchar(255)> NULL default '[]',
    `IMEI` array<varchar(255)> NULL default '[]',
    `CUID` array<varchar(255)> NULL default '[]',
    `MAC` array<varchar(255)> NULL default '[]',
    `IDFA` array<varchar(255)> NULL default '[]',
    `OAID` array<varchar(255)> NULL default '[]',
    `anonymous_id` array<varchar(255)> NULL default '[]'
    ) ENGINE=OLAP
    UNIQUE KEY(`oneId`)
    COMMENT 'id mapping 表'
    DISTRIBUTED BY HASH(`oneId`) BUCKETS 10
    PROPERTIES (
       "replication_allocation" = "tag.location.default: 3",
       "is_being_synced" = "false",
       "storage_medium" = "hdd",
       "storage_format" = "V2",
       "enable_unique_key_merge_on_write" = "true",
       "light_schema_change" = "true",
       "disable_auto_compaction" = "false",
       "enable_single_replica_compaction" = "false",
       "enable_mow_light_delete" = "false"
);
CREATE TABLE IF NOT EXISTS `user_profile_^&` (
             `oneId` VARCHAR(64) DEFAULT "全局唯一id",
             `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间',
             `deepsight_update_datetime` datetime NULL COMMENT '更新时间',
             `user_id` VARCHAR(255) NULL COMMENT '用户id',
             `user_name` VARCHAR(255) NULL COMMENT '用户昵称',
             `age` INT NULL COMMENT '用户年龄',
             `age_group` VARCHAR(255) NULL COMMENT '用户年龄段',
             `city` VARCHAR(255) NULL COMMENT '用户城市',
             `gender` VARCHAR(128) NULL COMMENT '用户性别',
             `register_time` datetime NULL COMMENT '注册时间',
             `update_time` datetime NULL COMMENT '更新时间',
             `tags` VARCHAR(255) NULL COMMENT '用户标签',
             `area` VARCHAR(255) NULL COMMENT '商圈',
             `country` VARCHAR(255) NULL COMMENT '用户国家',
             `device_id` VARCHAR(255) NULL COMMENT '设备标识',
             `device_model` VARCHAR(255) NULL COMMENT '设备型号',
             `district` VARCHAR(255) NULL COMMENT '用户所在区县',
             `membership_level` VARCHAR(255) NULL COMMENT '会员等级',
             `os` VARCHAR(255) NULL COMMENT '操作系统',
             `province` VARCHAR(255) NULL COMMENT '用户省份',
             `user_type` VARCHAR(255) NULL COMMENT '用户类型',
             `income_level` VARCHAR(255) NULL COMMENT '收入水平',
             `education_level` VARCHAR(255) NULL COMMENT '教育水平',
             `life_stage` VARCHAR(255) NULL COMMENT '人生阶段',
             `industry` VARCHAR(255) NULL COMMENT '所在行业',
             `occupation` VARCHAR(255) NULL COMMENT '职业类型',
             `city_level` VARCHAR(255) NULL COMMENT '常驻城市线级',
             `mobile` VARCHAR(255) NULL COMMENT '手机',
             `email_address` VARCHAR(255) NULL COMMENT '邮箱',
             `wechat_id` VARCHAR(255) NULL COMMENT '微信',
             `mobile_list` ARRAY < VARCHAR(128) > NULL DEFAULT "[]" COMMENT '手机号，多值',
             `email_list` ARRAY < VARCHAR(128) > NULL DEFAULT "[]" COMMENT '邮箱，多值',
             `source` VARCHAR(128) NULL COMMENT '来源',
             `predict_gender` VARCHAR(16) NULL DEFAULT "" COMMENT '预测性别',
             `predict_age_group` VARCHAR(64) NULL DEFAULT "" COMMENT '预测年龄',
             `predict_life_stage` VARCHAR(64) NULL DEFAULT "" COMMENT '预测人生阶段',
             `predict_marriage_status` VARCHAR(16) NULL DEFAULT "" COMMENT '预测婚姻状况',
             `predict_industry` VARCHAR(64) NULL DEFAULT "" COMMENT '预测所在行业',
             `predict_education_level` VARCHAR(64) NULL DEFAULT "" COMMENT '预测教育水平',
             `predict_occupation` VARCHAR(64) NULL DEFAULT "" COMMENT '预测职业类别',
             `predict_consume_level` VARCHAR(16) NULL DEFAULT "" COMMENT '预测消费水平',
             `predict_consume_intent` VARCHAR(512) NULL DEFAULT "" COMMENT '预测消费意愿',
             `predict_geographic_location` VARCHAR(512) NULL DEFAULT "" COMMENT '预测地理位置',
             `predict_interests` VARCHAR(2048) NULL DEFAULT "" COMMENT '预测兴趣关注',
             `merge_gender` VARCHAR(16) NULL DEFAULT "" COMMENT '合并性别',
             `merge_age_group` VARCHAR(64) NULL DEFAULT "" COMMENT '合并年龄',
             `merge_life_stage` VARCHAR(64) NULL DEFAULT "" COMMENT '合并人生阶段',
             `merge_marriage_status` VARCHAR(16) NULL DEFAULT "" COMMENT '合并婚姻状况',
             `merge_industry` VARCHAR(64) NULL DEFAULT "" COMMENT '合并所在行业',
             `merge_education_level` VARCHAR(64) NULL DEFAULT "" COMMENT '合并教育水平',
             `merge_occupation` VARCHAR(64) NULL DEFAULT "" COMMENT '合并职业类别',
             `merge_consume_level` VARCHAR(16) NULL DEFAULT "" COMMENT '合并消费水平',
             `merge_consume_intent` VARCHAR(512) NULL DEFAULT "" COMMENT '合并消费意愿',
             `merge_geographic_location` VARCHAR(512) NULL DEFAULT "" COMMENT '合并地理位置',
             `merge_interests` VARCHAR(2048) NULL DEFAULT "" COMMENT '合并兴趣关注',
             `bd_gender` VARCHAR(16) NULL DEFAULT "" COMMENT '百度性别',
             `bd_age_group` VARCHAR(32) NULL DEFAULT "" COMMENT '百度年龄',
             `bd_life_stage` VARCHAR(32) NULL DEFAULT "" COMMENT '百度人生阶段',
             `bd_marriage_status` VARCHAR(8) NULL DEFAULT "" COMMENT '百度婚姻状况',
             `bd_industry` VARCHAR(32) NULL DEFAULT "" COMMENT '百度所在行业',
             `bd_education_level` VARCHAR(32) NULL DEFAULT "" COMMENT '百度教育水平',
             `bd_occupation` VARCHAR(32) NULL DEFAULT "" COMMENT '百度职业类别',
             `bd_consume_level` VARCHAR(8) NULL DEFAULT "" COMMENT '百度消费水平',
             `bd_consume_intent` VARCHAR(512) NULL DEFAULT "" COMMENT '百度消费意愿',
             `bd_geographic_location` VARCHAR(512) NULL DEFAULT "" COMMENT '百度地理位置',
             `bd_interests` VARCHAR(2048) NULL DEFAULT "" COMMENT '百度兴趣关注',
             `DEVICEID` VARCHAR(255) NULL DEFAULT "" COMMENT '设备IDvarchar',
             `IMEI` VARCHAR(255) NULL DEFAULT "" COMMENT '国际移动设备身份码',
             `BAIDUID` VARCHAR(255) NULL DEFAULT "" COMMENT '百度域浏览器cookie',
             `CUID` VARCHAR(255) NULL DEFAULT "" COMMENT '百度定义移动设备唯一标示',
             `USERID` VARCHAR(255) NULL DEFAULT "" COMMENT '百度注册id',
             `MAC` VARCHAR(255) NULL DEFAULT "" COMMENT 'Mac 地址',
             `UNIONID` VARCHAR(255) NULL DEFAULT "" COMMENT '微信公众号用户UnionID',
             `IDFA` VARCHAR(255) NULL DEFAULT "" COMMENT 'apple提供给广告主的设备唯一ID',
             `OAID` VARCHAR(255) NULL DEFAULT "" COMMENT 'OAID的设备标识',
             `anonymous_id` VARCHAR(255) NULL DEFAULT "" COMMENT '匿名访客ID'
) ENGINE = OLAP UNIQUE KEY (`oneId`) COMMENT '用户档案表' DISTRIBUTED BY HASH (`oneId`) BUCKETS 10 PROPERTIES (
        "replication_allocation" = "tag.location.default: 3",
        "is_being_synced" = "false",
        "storage_medium" = "hdd",
        "storage_format" = "V2",
        "light_schema_change" = "true",
        "disable_auto_compaction" = "false",
        "enable_single_replica_compaction" = "false",
        "enable_mow_light_delete" = "false",
        "enable_unique_key_merge_on_write" = "true"
    );
ALTER TABLE mock_user_^& ADD COLUMN `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间';
ALTER TABLE mock_user_^& ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE mock_user_^& ADD COLUMN `mobile_list` ARRAY<VARCHAR(128)> NULL default '[]'  COMMENT '手机号，多值';
ALTER TABLE mock_user_^& ADD COLUMN `email_list` ARRAY<VARCHAR(128)> NULL default '[]'  COMMENT '邮箱，多值';
ALTER TABLE mock_user_^& ADD COLUMN `source` VARCHAR(128) NULL COMMENT '来源';
ALTER TABLE mock_user_^& RENAME COLUMN `device_id` `DEVICEID`;
ALTER TABLE mock_user_^& ADD COLUMN `IMEI` varchar(255) NULL DEFAULT '' COMMENT '国际移动设备身份码';
ALTER TABLE mock_user_^& ADD COLUMN `BAIDUID` varchar(255) NULL DEFAULT '' COMMENT '百度域浏览器cookie';
ALTER TABLE mock_user_^& ADD COLUMN `CUID` varchar(255) NULL DEFAULT '' COMMENT '百度定义移动设备唯一标示';
ALTER TABLE mock_user_^& ADD COLUMN `USERID` varchar(255) NULL DEFAULT '' COMMENT '百度注册id';
ALTER TABLE mock_user_^& ADD COLUMN `MAC` varchar(255) NULL DEFAULT '' COMMENT 'Mac 地址';
ALTER TABLE mock_user_^& ADD COLUMN `UNIONID` varchar(255) NULL DEFAULT '' COMMENT '微信公众号用户UnionID';
ALTER TABLE mock_user_^& ADD COLUMN `IDFA` varchar(255) NULL DEFAULT '' COMMENT 'apple提供给广告主的设备唯一ID';
ALTER TABLE mock_user_^& ADD COLUMN `OAID` varchar(255) NULL DEFAULT '' COMMENT 'OAID的设备标识';
ALTER TABLE mock_user_^& ADD COLUMN `anonymous_id` varchar(255) NULL DEFAULT '' COMMENT '匿名访客ID';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `BAIDUID` varchar(255) NULL DEFAULT '' COMMENT '百度域浏览器cookie';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `UNIONID` varchar(255) NULL DEFAULT '' COMMENT '微信公众号用户UnionID';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `IMEI` varchar(255) NULL DEFAULT '' COMMENT '国际移动设备身份码';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `CUID` varchar(255) NULL DEFAULT '' COMMENT '百度定义移动设备唯一标示';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `MAC` varchar(255) NULL DEFAULT '' COMMENT 'Mac地址';
ALTER TABLE keyue_conversation_record_^& ADD COLUMN `anonymous_id` varchar(255) NULL DEFAULT '' COMMENT '匿名访客ID';
ALTER TABLE keyue_conversation_record_^& RENAME COLUMN `uid` `user_id`;
ALTER TABLE aiob_conversation_session_service_^& ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE aiob_conversation_session_service_^& ADD COLUMN `BAIDUID` varchar(255) NULL DEFAULT '' COMMENT '百度域浏览器cookie';
ALTER TABLE aiob_conversation_session_service_^& ADD COLUMN `IMEI` varchar(255) NULL DEFAULT '' COMMENT '国际移动设备身份码';
ALTER TABLE aiob_conversation_session_service_^& ADD COLUMN `CUID` varchar(255) NULL DEFAULT '' COMMENT '百度定义移动设备唯一标示';
ALTER TABLE aiob_conversation_session_service_^& ADD COLUMN `MAC` varchar(255) NULL DEFAULT '' COMMENT 'Mac 地址';
ALTER TABLE aiob_conversation_record_^& ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE memory_extract_info_^& ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';